<?php
/**
 * User Authentication API
 * Handles user login, registration, and session management
 */

require_once '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Start session
session_start();

try {
    $pdo = getPDOConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'login':
            handleUserLogin($pdo);
            break;
            
        case 'register':
            handleUserRegistration($pdo);
            break;
            
        case 'logout':
            handleUserLogout();
            break;
            
        case 'check':
            handleAuthCheck();
            break;
            
        case 'profile':
            handleUserProfile($pdo);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle user login
 */
function handleUserLogin($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['email']) || !isset($input['password'])) {
        throw new Exception('البريد الإلكتروني وكلمة المرور مطلوبان');
    }
    
    $email = trim($input['email']);
    $password = $input['password'];
    
    // Check user credentials
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($password, $user['password'])) {
        throw new Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    
    // Update last login
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);
    
    // Set session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
    $_SESSION['user_role'] = $user['role_id'];
    $_SESSION['user_subscription'] = $user['subscription_id'];
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'user' => [
            'id' => $user['id'],
            'email' => $user['email'],
            'name' => $user['first_name'] . ' ' . $user['last_name'],
            'role_id' => $user['role_id'],
            'subscription_id' => $user['subscription_id']
        ]
    ]);
}

/**
 * Handle user registration
 */
function handleUserRegistration($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required = ['email', 'password', 'first_name', 'last_name'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            throw new Exception("الحقل {$field} مطلوب");
        }
    }
    
    $email = trim($input['email']);
    $password = $input['password'];
    $firstName = trim($input['first_name']);
    $lastName = trim($input['last_name']);
    $phone = trim($input['phone'] ?? '');
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('صيغة البريد الإلكتروني غير صحيحة');
    }
    
    // Check if email already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('البريد الإلكتروني مستخدم بالفعل');
    }
    
    // Validate password strength
    if (strlen($password) < 8) {
        throw new Exception('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }
    
    // Generate username from email
    $username = explode('@', $email)[0] . '_' . time();
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert user
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, first_name, last_name, phone, role_id, subscription_id, status, email_verified)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', 1)
    ");
    
    $stmt->execute([
        $username,
        $email,
        $hashedPassword,
        $firstName,
        $lastName,
        $phone,
        4, // Default user role
        1  // Default subscription
    ]);
    
    $userId = $pdo->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء الحساب بنجاح',
        'user_id' => $userId
    ]);
}

/**
 * Handle user logout
 */
function handleUserLogout() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Clear user session
    unset($_SESSION['user_id']);
    unset($_SESSION['user_email']);
    unset($_SESSION['user_name']);
    unset($_SESSION['user_role']);
    unset($_SESSION['user_subscription']);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تسجيل الخروج بنجاح'
    ]);
}

/**
 * Handle authentication check
 */
function handleAuthCheck() {
    $isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    
    echo json_encode([
        'success' => true,
        'logged_in' => $isLoggedIn,
        'user' => $isLoggedIn ? [
            'id' => $_SESSION['user_id'],
            'email' => $_SESSION['user_email'],
            'name' => $_SESSION['user_name'],
            'role_id' => $_SESSION['user_role'],
            'subscription_id' => $_SESSION['user_subscription']
        ] : null
    ]);
}

/**
 * Handle user profile
 */
function handleUserProfile($pdo) {
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get user profile
        $stmt = $pdo->prepare("SELECT id, username, email, first_name, last_name, phone, status, created_at, last_login FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        echo json_encode([
            'success' => true,
            'user' => $user
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Update user profile
        $input = json_decode(file_get_contents('php://input'), true);
        
        $updates = [];
        $params = [];
        
        if (isset($input['first_name'])) {
            $updates[] = "first_name = ?";
            $params[] = trim($input['first_name']);
        }
        
        if (isset($input['last_name'])) {
            $updates[] = "last_name = ?";
            $params[] = trim($input['last_name']);
        }
        
        if (isset($input['phone'])) {
            $updates[] = "phone = ?";
            $params[] = trim($input['phone']);
        }
        
        if (isset($input['password']) && !empty($input['password'])) {
            if (strlen($input['password']) < 8) {
                throw new Exception('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            }
            $updates[] = "password = ?";
            $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }
        
        if (empty($updates)) {
            throw new Exception('لا توجد بيانات للتحديث');
        }
        
        $params[] = $_SESSION['user_id'];
        
        $sql = "UPDATE users SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الملف الشخصي بنجاح'
        ]);
    }
}

/**
 * Check if user is logged in
 */
function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}
?>
