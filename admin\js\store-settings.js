/**
 * Store Settings Management System
 * Handles store-specific configurations and settings
 */

// Store settings data structure
let storeSettings = {
    storeInfo: {
        name: '',
        description: '',
        logo: '',
        favicon: '',
        address: '',
        phone: '',
        email: '',
        website: ''
    },
    businessDetails: {
        businessType: '',
        taxNumber: '',
        commercialRegister: '',
        bankAccount: '',
        iban: ''
    },
    policies: {
        termsOfService: '',
        privacyPolicy: '',
        returnPolicy: '',
        shippingPolicy: ''
    },
    branding: {
        primaryColor: '#667eea',
        secondaryColor: '#764ba2',
        accentColor: '#f093fb',
        fontFamily: 'Cairo, sans-serif'
    },
    social: {
        facebook: '',
        instagram: '',
        twitter: '',
        youtube: '',
        tiktok: ''
    }
};

/**
 * Initialize store settings
 */
function initializeStoreSettings() {
    console.log('Initializing store settings...');
    
    // Load store settings content
    loadStoreSettingsContent();
    
    console.log('Store settings initialized successfully');
}

/**
 * Load store settings content
 */
function loadStoreSettingsContent() {
    const content = `
        <div class="store-settings-container">
            <!-- Store Information Section -->
            <div class="settings-card">
                <h3><i class="fas fa-store"></i> معلومات المتجر</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="storeName">اسم المتجر</label>
                        <input type="text" id="storeName" class="form-control" placeholder="أدخل اسم المتجر">
                    </div>
                    <div class="form-group">
                        <label for="storeEmail">البريد الإلكتروني</label>
                        <input type="email" id="storeEmail" class="form-control" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="storePhone">رقم الهاتف</label>
                        <input type="tel" id="storePhone" class="form-control" placeholder="+213 XXX XXX XXX">
                    </div>
                    <div class="form-group">
                        <label for="storeWebsite">الموقع الإلكتروني</label>
                        <input type="url" id="storeWebsite" class="form-control" placeholder="https://example.com">
                    </div>
                    <div class="form-group full-width">
                        <label for="storeDescription">وصف المتجر</label>
                        <textarea id="storeDescription" class="form-control" rows="3" placeholder="وصف مختصر عن المتجر"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="storeAddress">العنوان</label>
                        <textarea id="storeAddress" class="form-control" rows="2" placeholder="العنوان الكامل للمتجر"></textarea>
                    </div>
                </div>
            </div>

            <!-- Business Details Section -->
            <div class="settings-card">
                <h3><i class="fas fa-building"></i> التفاصيل التجارية</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="businessType">نوع النشاط التجاري</label>
                        <select id="businessType" class="form-control">
                            <option value="">اختر نوع النشاط</option>
                            <option value="retail">تجارة تجزئة</option>
                            <option value="wholesale">تجارة جملة</option>
                            <option value="services">خدمات</option>
                            <option value="manufacturing">تصنيع</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taxNumber">الرقم الضريبي</label>
                        <input type="text" id="taxNumber" class="form-control" placeholder="الرقم الضريبي">
                    </div>
                    <div class="form-group">
                        <label for="commercialRegister">السجل التجاري</label>
                        <input type="text" id="commercialRegister" class="form-control" placeholder="رقم السجل التجاري">
                    </div>
                    <div class="form-group">
                        <label for="bankAccount">رقم الحساب البنكي</label>
                        <input type="text" id="bankAccount" class="form-control" placeholder="رقم الحساب البنكي">
                    </div>
                    <div class="form-group">
                        <label for="iban">رقم IBAN</label>
                        <input type="text" id="iban" class="form-control" placeholder="DZ XX XXXX XXXX XXXX XXXX XXXX">
                    </div>
                </div>
            </div>

            <!-- Branding Section -->
            <div class="settings-card">
                <h3><i class="fas fa-palette"></i> الهوية البصرية</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="primaryColor">اللون الأساسي</label>
                        <div class="color-input-group">
                            <input type="color" id="primaryColor" class="color-picker" value="#667eea">
                            <input type="text" class="form-control color-text" value="#667eea" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="secondaryColor">اللون الثانوي</label>
                        <div class="color-input-group">
                            <input type="color" id="secondaryColor" class="color-picker" value="#764ba2">
                            <input type="text" class="form-control color-text" value="#764ba2" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="accentColor">لون التمييز</label>
                        <div class="color-input-group">
                            <input type="color" id="accentColor" class="color-picker" value="#f093fb">
                            <input type="text" class="form-control color-text" value="#f093fb" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="fontFamily">خط النص</label>
                        <select id="fontFamily" class="form-control">
                            <option value="Cairo, sans-serif">Cairo</option>
                            <option value="Amiri, serif">Amiri</option>
                            <option value="Tajawal, sans-serif">Tajawal</option>
                            <option value="Almarai, sans-serif">Almarai</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Social Media Section -->
            <div class="settings-card">
                <h3><i class="fas fa-share-alt"></i> وسائل التواصل الاجتماعي</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="facebook"><i class="fab fa-facebook"></i> فيسبوك</label>
                        <input type="url" id="facebook" class="form-control" placeholder="https://facebook.com/yourpage">
                    </div>
                    <div class="form-group">
                        <label for="instagram"><i class="fab fa-instagram"></i> إنستغرام</label>
                        <input type="url" id="instagram" class="form-control" placeholder="https://instagram.com/yourpage">
                    </div>
                    <div class="form-group">
                        <label for="twitter"><i class="fab fa-twitter"></i> تويتر</label>
                        <input type="url" id="twitter" class="form-control" placeholder="https://twitter.com/yourpage">
                    </div>
                    <div class="form-group">
                        <label for="youtube"><i class="fab fa-youtube"></i> يوتيوب</label>
                        <input type="url" id="youtube" class="form-control" placeholder="https://youtube.com/yourchannel">
                    </div>
                    <div class="form-group">
                        <label for="tiktok"><i class="fab fa-tiktok"></i> تيك توك</label>
                        <input type="url" id="tiktok" class="form-control" placeholder="https://tiktok.com/@yourpage">
                    </div>
                </div>
            </div>

            <!-- Policies Section -->
            <div class="settings-card">
                <h3><i class="fas fa-file-contract"></i> السياسات والشروط</h3>
                <div class="form-group">
                    <label for="termsOfService">شروط الخدمة</label>
                    <textarea id="termsOfService" class="form-control tinymce" rows="6" placeholder="أدخل شروط الخدمة..."></textarea>
                </div>
                <div class="form-group">
                    <label for="privacyPolicy">سياسة الخصوصية</label>
                    <textarea id="privacyPolicy" class="form-control tinymce" rows="6" placeholder="أدخل سياسة الخصوصية..."></textarea>
                </div>
                <div class="form-group">
                    <label for="returnPolicy">سياسة الإرجاع</label>
                    <textarea id="returnPolicy" class="form-control tinymce" rows="6" placeholder="أدخل سياسة الإرجاع..."></textarea>
                </div>
                <div class="form-group">
                    <label for="shippingPolicy">سياسة الشحن</label>
                    <textarea id="shippingPolicy" class="form-control tinymce" rows="6" placeholder="أدخل سياسة الشحن..."></textarea>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button type="button" class="action-button" onclick="saveStoreSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button type="button" class="action-button secondary" onclick="resetStoreSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
                <button type="button" class="action-button info" onclick="exportStoreSettings()">
                    <i class="fas fa-download"></i> تصدير الإعدادات
                </button>
            </div>
        </div>
    `;

    document.getElementById('storeSettingsContent').innerHTML = content;
    
    // Initialize color pickers
    initializeColorPickers();
    
    // Load existing settings
    loadExistingSettings();
    
    // Initialize TinyMCE for policy textareas
    if (typeof tinymce !== 'undefined') {
        initializePolicyEditors();
    }
}

/**
 * Initialize color pickers
 */
function initializeColorPickers() {
    const colorPickers = document.querySelectorAll('.color-picker');
    colorPickers.forEach(picker => {
        picker.addEventListener('change', function() {
            const textInput = this.parentNode.querySelector('.color-text');
            textInput.value = this.value;
        });
    });
}

/**
 * Load existing settings from server
 */
async function loadExistingSettings() {
    try {
        // This would typically load from an API
        // For now, we'll use default values
        console.log('Loading existing store settings...');
        
        // Populate form with existing data
        populateForm(storeSettings);
        
    } catch (error) {
        console.error('Error loading store settings:', error);
        showNotification('خطأ في تحميل إعدادات المتجر', 'error');
    }
}

/**
 * Populate form with settings data
 */
function populateForm(settings) {
    // Store info
    document.getElementById('storeName').value = settings.storeInfo.name || '';
    document.getElementById('storeEmail').value = settings.storeInfo.email || '';
    document.getElementById('storePhone').value = settings.storeInfo.phone || '';
    document.getElementById('storeWebsite').value = settings.storeInfo.website || '';
    document.getElementById('storeDescription').value = settings.storeInfo.description || '';
    document.getElementById('storeAddress').value = settings.storeInfo.address || '';
    
    // Business details
    document.getElementById('businessType').value = settings.businessDetails.businessType || '';
    document.getElementById('taxNumber').value = settings.businessDetails.taxNumber || '';
    document.getElementById('commercialRegister').value = settings.businessDetails.commercialRegister || '';
    document.getElementById('bankAccount').value = settings.businessDetails.bankAccount || '';
    document.getElementById('iban').value = settings.businessDetails.iban || '';
    
    // Branding
    document.getElementById('primaryColor').value = settings.branding.primaryColor || '#667eea';
    document.getElementById('secondaryColor').value = settings.branding.secondaryColor || '#764ba2';
    document.getElementById('accentColor').value = settings.branding.accentColor || '#f093fb';
    document.getElementById('fontFamily').value = settings.branding.fontFamily || 'Cairo, sans-serif';
    
    // Social media
    document.getElementById('facebook').value = settings.social.facebook || '';
    document.getElementById('instagram').value = settings.social.instagram || '';
    document.getElementById('twitter').value = settings.social.twitter || '';
    document.getElementById('youtube').value = settings.social.youtube || '';
    document.getElementById('tiktok').value = settings.social.tiktok || '';
    
    // Update color text inputs
    document.querySelectorAll('.color-picker').forEach(picker => {
        const textInput = picker.parentNode.querySelector('.color-text');
        textInput.value = picker.value;
    });
}

/**
 * Save store settings
 */
async function saveStoreSettings() {
    try {
        // Collect form data
        const formData = collectFormData();
        
        // Validate required fields
        if (!validateSettings(formData)) {
            return;
        }
        
        // Show loading
        showNotification('جاري حفظ الإعدادات...', 'info');
        
        // Save to server (API call would go here)
        console.log('Saving store settings:', formData);
        
        // Update local storage
        storeSettings = formData;
        
        // Show success message
        showNotification('تم حفظ إعدادات المتجر بنجاح', 'success');
        
    } catch (error) {
        console.error('Error saving store settings:', error);
        showNotification('خطأ في حفظ الإعدادات', 'error');
    }
}

/**
 * Collect form data
 */
function collectFormData() {
    return {
        storeInfo: {
            name: document.getElementById('storeName').value,
            email: document.getElementById('storeEmail').value,
            phone: document.getElementById('storePhone').value,
            website: document.getElementById('storeWebsite').value,
            description: document.getElementById('storeDescription').value,
            address: document.getElementById('storeAddress').value
        },
        businessDetails: {
            businessType: document.getElementById('businessType').value,
            taxNumber: document.getElementById('taxNumber').value,
            commercialRegister: document.getElementById('commercialRegister').value,
            bankAccount: document.getElementById('bankAccount').value,
            iban: document.getElementById('iban').value
        },
        branding: {
            primaryColor: document.getElementById('primaryColor').value,
            secondaryColor: document.getElementById('secondaryColor').value,
            accentColor: document.getElementById('accentColor').value,
            fontFamily: document.getElementById('fontFamily').value
        },
        social: {
            facebook: document.getElementById('facebook').value,
            instagram: document.getElementById('instagram').value,
            twitter: document.getElementById('twitter').value,
            youtube: document.getElementById('youtube').value,
            tiktok: document.getElementById('tiktok').value
        },
        policies: {
            termsOfService: document.getElementById('termsOfService').value,
            privacyPolicy: document.getElementById('privacyPolicy').value,
            returnPolicy: document.getElementById('returnPolicy').value,
            shippingPolicy: document.getElementById('shippingPolicy').value
        }
    };
}

/**
 * Validate settings
 */
function validateSettings(settings) {
    if (!settings.storeInfo.name.trim()) {
        showNotification('اسم المتجر مطلوب', 'error');
        document.getElementById('storeName').focus();
        return false;
    }
    
    if (!settings.storeInfo.email.trim()) {
        showNotification('البريد الإلكتروني مطلوب', 'error');
        document.getElementById('storeEmail').focus();
        return false;
    }
    
    return true;
}

/**
 * Reset store settings
 */
function resetStoreSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        populateForm({
            storeInfo: {},
            businessDetails: {},
            branding: {
                primaryColor: '#667eea',
                secondaryColor: '#764ba2',
                accentColor: '#f093fb',
                fontFamily: 'Cairo, sans-serif'
            },
            social: {},
            policies: {}
        });
        showNotification('تم إعادة تعيين الإعدادات', 'info');
    }
}

/**
 * Export store settings
 */
function exportStoreSettings() {
    const settings = collectFormData();
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'store-settings.json';
    link.click();
    
    showNotification('تم تصدير الإعدادات بنجاح', 'success');
}

/**
 * Initialize TinyMCE for policy editors
 */
function initializePolicyEditors() {
    const policyTextareas = ['termsOfService', 'privacyPolicy', 'returnPolicy', 'shippingPolicy'];
    
    policyTextareas.forEach(id => {
        if (document.getElementById(id)) {
            tinymce.init({
                selector: `#${id}`,
                height: 200,
                language: 'ar',
                directionality: 'rtl',
                plugins: 'lists link',
                toolbar: 'undo redo | bold italic | bullist numlist | link',
                menubar: false,
                branding: false
            });
        }
    });
}

/**
 * Show notification
 */
function showNotification(message, type) {
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            case 'info':
                notificationManager.showInfo(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        alert(message);
    }
}

// Make functions globally available
window.initializeStoreSettings = initializeStoreSettings;
window.saveStoreSettings = saveStoreSettings;
window.resetStoreSettings = resetStoreSettings;
window.exportStoreSettings = exportStoreSettings;
