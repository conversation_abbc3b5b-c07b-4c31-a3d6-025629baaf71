<?php

/**
 * AI API Endpoint for Mossaab Landing Page
 * Handles AI text generation requests
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../security.php';
require_once __DIR__ . '/../AIManager.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Set JSON content type header first
    header('Content-Type: application/json; charset=utf-8');

    // Initialize security
    Security::init();

    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    // Debug logging if enabled
    if (Config::get('APP_DEBUG', false)) {
        error_log("AI API: Action = " . $action);
        error_log("AI API: Request method = " . $_SERVER['REQUEST_METHOD']);
        error_log("AI API: GET params = " . json_encode($_GET));
        error_log("AI API: POST params = " . json_encode($_POST));
    }

    // Allow configuration actions even when AI is not available
    $configActions = ['get_config', 'update_config', 'test_connection'];

    // Get AI configuration
    $aiConfig = AIManager::getInstance();

    // Check if any AI provider is enabled (skip for configuration actions)
    if (!in_array($action, $configActions)) {
        $hasEnabledProvider = false;
        foreach (['openai', 'anthropic', 'gemini'] as $provider) {
            if ($aiConfig->isProviderEnabled($provider)) {
                $hasEnabledProvider = true;
                break;
            }
        }

        if (!$hasEnabledProvider) {
            throw new Exception('لم يتم تكوين أي مزود ذكاء اصطناعي', 400);
        }
    }

    switch ($action) {
        case 'generate_product_description':
            handleGenerateProductDescription($aiConfig);
            break;

        case 'generate_landing_page_title':
            handleGenerateLandingPageTitle($aiConfig);
            break;

        case 'generate_landing_page_content':
            handleGenerateLandingPageContent($aiConfig);
            break;

        case 'generate_meta_description':
            handleGenerateMetaDescription($aiConfig);
            break;

        case 'test_connection':
            handleTestConnection($aiConfig);
            break;

        case 'get_config':
            handleGetConfig($aiConfig);
            break;

        case 'update_config':
            handleUpdateConfig($aiConfig);
            break;

        default:
            throw new Exception('إجراء غير صالح', 400);
    }
} catch (Exception $e) {
    // Ensure clean output buffer
    if (ob_get_level()) {
        ob_clean();
    }

    $statusCode = $e->getCode() ?: 500;
    error_log('AI API Error: ' . $e->getMessage());

    // Set proper headers
    header('Content-Type: application/json; charset=utf-8');
    http_response_code($statusCode);

    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => $statusCode,
        'file' => basename(__FILE__)
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Handle product description generation
 */
function handleGenerateProductDescription($aiConfig)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['product'])) {
        throw new Exception('بيانات المنتج مطلوبة', 400);
    }

    try {
        $provider = $input['provider'] ?? getFirstEnabledProvider($aiConfig);
        $description = generateAIContent($aiConfig, $provider, [
            'type' => 'product_description',
            'product' => $input['product']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'description' => $description,
                'provider' => $provider
            ],
            'message' => 'تم إنشاء الوصف بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إنشاء الوصف: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Handle landing page title generation
 */
function handleGenerateLandingPageTitle($aiConfig)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['product'])) {
        throw new Exception('بيانات المنتج مطلوبة', 400);
    }

    try {
        $provider = $input['provider'] ?? getFirstEnabledProvider($aiConfig);
        $title = generateAIContent($aiConfig, $provider, [
            'type' => 'landing_page_title',
            'product' => $input['product']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'title' => $title,
                'provider' => $provider
            ],
            'message' => 'تم إنشاء العنوان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إنشاء العنوان: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Handle landing page content generation
 */
function handleGenerateLandingPageContent($aiConfig)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['product'])) {
        throw new Exception('بيانات المنتج مطلوبة', 400);
    }

    try {
        $provider = $input['provider'] ?? getFirstEnabledProvider($aiConfig);
        $content = generateAIContent($aiConfig, $provider, [
            'type' => 'landing_page_content',
            'product' => $input['product']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'content' => $content,
                'provider' => $provider
            ],
            'message' => 'تم إنشاء المحتوى بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إنشاء المحتوى: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Handle meta description generation
 */
function handleGenerateMetaDescription($aiConfig)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['product'])) {
        throw new Exception('بيانات المنتج مطلوبة', 400);
    }

    try {
        $provider = $input['provider'] ?? getFirstEnabledProvider($aiConfig);
        $metaDescription = generateAIContent($aiConfig, $provider, [
            'type' => 'meta_description',
            'product' => $input['product']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'meta_description' => $metaDescription,
                'provider' => $provider
            ],
            'message' => 'تم إنشاء وصف الميتا بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إنشاء وصف الميتا: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Test AI provider connection
 */
function handleTestConnection($aiConfig)
{
    global $conn;

    $provider = $_GET['provider'] ?? $_POST['provider'] ?? null;

    if (!$provider) {
        throw new Exception('مزود الذكاء الاصطناعي مطلوب', 400);
    }

    try {
        // Ensure ai_settings table exists
        createAISettingsTable($conn);

        // Get provider settings from database
        $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = ?");
        $stmt->execute([$provider]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get API key from .env if not in database
        $envApiKey = '';
        if ($provider === 'openai') {
            $envApiKey = $_ENV['OPENAI_API_KEY'] ?? '';
        } elseif ($provider === 'anthropic') {
            $envApiKey = $_ENV['ANTHROPIC_API_KEY'] ?? '';
        } elseif ($provider === 'gemini') {
            $envApiKey = $_ENV['GEMINI_API_KEY'] ?? '';
        }

        $apiKey = '';
        if ($settings) {
            $apiKey = !empty($settings['api_key']) ? $settings['api_key'] : $envApiKey;
        } else {
            $apiKey = $envApiKey;
        }

        if (empty($apiKey)) {
            throw new Exception('مفتاح API غير مُعين لمزود ' . $provider, 400);
        }

        // Test the connection based on provider
        $testResult = testProviderConnection($provider, $apiKey);

        echo json_encode([
            'success' => true,
            'data' => [
                'provider' => $provider,
                'api_key_source' => !empty($settings['api_key']) ? 'database' : 'env',
                'connection_test' => $testResult,
                'api_key_length' => strlen($apiKey)
            ],
            'message' => 'تم اختبار الاتصال بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'provider' => $provider
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Test connection to AI provider
 */
function testProviderConnection($provider, $apiKey)
{
    switch ($provider) {
        case 'openai':
            return testOpenAIConnection($apiKey);
        case 'anthropic':
            return testAnthropicConnection($apiKey);
        case 'gemini':
            return testGeminiConnection($apiKey);
        default:
            throw new Exception('مزود غير مدعوم: ' . $provider, 400);
    }
}

/**
 * Test OpenAI API connection
 */
function testOpenAIConnection($apiKey)
{
    $url = 'https://api.openai.com/v1/models';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return [
            'status' => 'error',
            'message' => 'خطأ في الاتصال: ' . $error
        ];
    }

    if ($httpCode === 200) {
        $data = json_decode($response, true);
        return [
            'status' => 'success',
            'message' => 'تم الاتصال بنجاح',
            'models_count' => isset($data['data']) ? count($data['data']) : 0
        ];
    } elseif ($httpCode === 401) {
        return [
            'status' => 'error',
            'message' => 'مفتاح API غير صالح'
        ];
    } else {
        return [
            'status' => 'error',
            'message' => 'خطأ HTTP: ' . $httpCode
        ];
    }
}

/**
 * Test Anthropic API connection
 */
function testAnthropicConnection($apiKey)
{
    // Placeholder for Anthropic API test
    return [
        'status' => 'info',
        'message' => 'اختبار Anthropic غير مُفعل حالياً'
    ];
}

/**
 * Test Gemini API connection
 */
function testGeminiConnection($apiKey)
{
    // Placeholder for Gemini API test
    return [
        'status' => 'info',
        'message' => 'اختبار Gemini غير مُفعل حالياً'
    ];
}

/**
 * Get AI provider configuration
 */
function handleGetConfig($aiConfig)
{
    global $conn;

    try {
        // Ensure ai_settings table exists
        createAISettingsTable($conn);

        $providers = ['openai', 'anthropic', 'gemini'];
        $config = [];

        foreach ($providers as $provider) {
            // Get settings from database
            $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = ?");
            $stmt->execute([$provider]);
            $dbSettings = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get API key from .env if not in database or database key is empty
            $envApiKey = '';
            if ($provider === 'openai') {
                $envApiKey = $_ENV['OPENAI_API_KEY'] ?? '';
            } elseif ($provider === 'anthropic') {
                $envApiKey = $_ENV['ANTHROPIC_API_KEY'] ?? '';
            } elseif ($provider === 'gemini') {
                $envApiKey = $_ENV['GEMINI_API_KEY'] ?? '';
            }

            // Use database settings if available, otherwise use defaults with .env API key
            if ($dbSettings) {
                $apiKey = !empty($dbSettings['api_key']) ? $dbSettings['api_key'] : $envApiKey;
                $config[$provider] = [
                    'api_key' => $apiKey,
                    'model' => $dbSettings['model'],
                    'max_tokens' => (int)$dbSettings['max_tokens'],
                    'temperature' => (float)$dbSettings['temperature'],
                    'is_active' => (bool)$dbSettings['is_active'],
                    'has_env_key' => !empty($envApiKey),
                    'using_env_key' => empty($dbSettings['api_key']) && !empty($envApiKey),
                    'status' => !empty($apiKey) ? 'configured' : 'not_configured'
                ];
            } else {
                // Default settings with .env API key
                $defaultModels = [
                    'openai' => 'gpt-3.5-turbo',
                    'anthropic' => 'claude-3-sonnet',
                    'gemini' => 'gemini-pro'
                ];

                $config[$provider] = [
                    'api_key' => $envApiKey,
                    'model' => $defaultModels[$provider],
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                    'is_active' => !empty($envApiKey),
                    'has_env_key' => !empty($envApiKey),
                    'using_env_key' => !empty($envApiKey),
                    'status' => !empty($envApiKey) ? 'configured' : 'not_configured'
                ];
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $config,
            'message' => 'تم جلب الإعدادات بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception('فشل في جلب الإعدادات: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Create AI settings table if it doesn't exist
 */
function createAISettingsTable($conn)
{
    $sql = "
    CREATE TABLE IF NOT EXISTS ai_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        provider VARCHAR(50) NOT NULL UNIQUE,
        api_key TEXT,
        model VARCHAR(100),
        max_tokens INT DEFAULT 1000,
        temperature DECIMAL(3,2) DEFAULT 0.7,
        is_active BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $conn->exec($sql);

    // Insert default providers if they don't exist
    $defaultProviders = [
        ['openai', '', 'gpt-3.5-turbo', 1000, 0.7, 0],
        ['anthropic', '', 'claude-3-sonnet', 1000, 0.7, 0],
        ['gemini', '', 'gemini-pro', 1000, 0.7, 0]
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO ai_settings (provider, api_key, model, max_tokens, temperature, is_active) VALUES (?, ?, ?, ?, ?, ?)");

    foreach ($defaultProviders as $provider) {
        $stmt->execute($provider);
    }
}

/**
 * Update AI provider configuration
 */
function handleUpdateConfig($aiConfig)
{
    global $conn;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || empty($input['provider'])) {
        throw new Exception('بيانات غير صالحة', 400);
    }

    try {
        $provider = $input['provider'];
        $apiKey = $input['api_key'] ?? '';
        $model = $input['model'] ?? '';
        $maxTokens = $input['max_tokens'] ?? 1000;
        $temperature = $input['temperature'] ?? 0.7;
        $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : false;

        // Validate API key format
        if ($apiKey && strlen($apiKey) < 10) {
            throw new Exception('مفتاح API غير صالح لمزود ' . $provider, 400);
        }

        // Ensure ai_settings table exists
        createAISettingsTable($conn);

        // Check if provider exists, if not insert it
        $stmt = $conn->prepare("SELECT id FROM ai_settings WHERE provider = ?");
        $stmt->execute([$provider]);
        $exists = $stmt->fetch();

        if ($exists) {
            // Update existing provider
            $stmt = $conn->prepare("
                UPDATE ai_settings
                SET api_key = ?, model = ?, max_tokens = ?, temperature = ?, is_active = ?, updated_at = NOW()
                WHERE provider = ?
            ");
            $stmt->execute([$apiKey, $model, $maxTokens, $temperature, $isActive, $provider]);
        } else {
            // Insert new provider
            $stmt = $conn->prepare("
                INSERT INTO ai_settings (provider, api_key, model, max_tokens, temperature, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$provider, $apiKey, $model, $maxTokens, $temperature, $isActive]);
        }

        // Get updated settings
        $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = ?");
        $stmt->execute([$provider]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ إعدادات ' . $provider . ' بنجاح',
            'data' => [
                'provider' => $provider,
                'settings' => $settings
            ],
            'message' => 'تم تحديث الإعدادات بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحديث الإعدادات: ' . $e->getMessage(), $e->getCode() ?: 500);
    }
}

/**
 * Get the first enabled AI provider
 */
function getFirstEnabledProvider($aiConfig)
{
    $providers = ['openai', 'anthropic', 'gemini'];

    foreach ($providers as $provider) {
        if ($aiConfig->isProviderEnabled($provider)) {
            return $provider;
        }
    }

    throw new Exception('لا يوجد مزود ذكاء اصطناعي متاح', 400);
}

/**
 * Generate AI content using the specified provider
 */
function generateAIContent($aiConfig, $provider, $params)
{
    if (!$aiConfig->isProviderEnabled($provider)) {
        throw new Exception('مزود الذكاء الاصطناعي غير متاح: ' . $provider, 400);
    }

    // Call the appropriate AI provider service
    // This is a placeholder - implement actual AI provider integration
    $apiKey = $aiConfig->getApiKey($provider);

    // TODO: Implement actual AI content generation
    return "Generated content using $provider";
}
