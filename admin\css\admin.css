:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --border-radius-lg: 16px;
    --spacing-lg: 30px;
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --color-text-primary: #1e293b;
    --color-text-secondary: #64748b;
    --color-border: #e2e8f0;
    --color-background: #f8fafc;
    writing-mode: horizontal-tb;
    direction: rtl;
    text-orientation: mixed;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
    direction: rtl;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: #f8fafc;
}

/* Enhanced Modal Styles */
.enhanced-modal .modal-content {
    width: 95% !important;
    max-width: 1400px !important;
    height: 95vh !important;
    max-height: 95vh !important;
    margin: 2.5vh auto !important;
    overflow-y: auto !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
}

.enhanced-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.enhanced-modal .modal-header h3 {
    font-size: 1.5rem;
    margin: 0;
    font-weight: 600;
}

.content-builder {
    margin: 20px 0;
}

.content-blocks-container {
    min-height: 200px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: white;
    transition: border-color 0.3s ease;
}

.content-blocks-container:hover {
    border-color: #667eea;
}

.content-block {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-block:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.content-block .fas.fa-grip-vertical {
    cursor: move;
    color: #6c757d;
    margin-left: 10px;
}

.add-block-btn, .preview-btn {
    transition: all 0.3s ease;
    font-weight: 500;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.add-block-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.preview-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* Enhanced form styling */
.enhanced-modal .form-group {
    margin-bottom: 25px;
}

.enhanced-modal .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.enhanced-modal input[type="text"],
.enhanced-modal input[type="url"],
.enhanced-modal input[type="file"],
.enhanced-modal select,
.enhanced-modal textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.enhanced-modal input:focus,
.enhanced-modal select:focus,
.enhanced-modal textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Mobile First Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .enhanced-modal .modal-content {
        width: 98% !important;
        height: 98vh !important;
        margin: 1vh auto !important;
    }

    .content-builder {
        padding: 15px;
    }

    .add-block-btn, .preview-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .sidebar {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar.mobile-open {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        padding: 20px 15px;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1001;
        background: #3b82f6;
        color: white;
        border: none;
        padding: 14px;
        border-radius: 8px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        min-height: 48px;
        min-width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-menu-toggle:active {
        transform: scale(0.95);
        background: #2563eb;
    }

    .mobile-menu-toggle i {
        font-size: 1.2rem;
    }
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.sidebar .logo {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar .logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    text-align: center;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-nav {
    padding: 20px 0;
}

.admin-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav ul li {
    margin: 8px 15px;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    position: relative;
    -webkit-user-select: none;
    user-select: none;
    font-weight: 500;
}

.admin-nav ul li:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-nav ul li.active {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.admin-nav ul li.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: #ffffff;
    border-radius: 2px 0 0 2px;
}

.admin-nav ul li i {
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    opacity: 0.9;
}

.admin-nav ul li span {
    font-size: 0.95rem;
    font-weight: 500;
}

.mobile-menu-toggle {
    display: none;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 40px;
    background: #f8fafc;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

/* Content Header */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Refresh Button */
.refresh-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.refresh-button:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.refresh-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.refresh-button i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.refresh-button:hover i {
    transform: rotate(180deg);
}

.refresh-button.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Template Selection Styles */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.template-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    text-align: center;
}

.template-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.template-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.template-card .template-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.template-card .template-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.template-card .template-description {
    font-size: 0.9rem;
    opacity: 0.8;
}

.template-card.selected .template-description {
    opacity: 0.9;
}

/* Modal Steps */
.modal-step {
    display: none;
}

.modal-step.active {
    display: block;
}

.step-actions {
    text-align: center;
    margin-top: 20px;
}

/* Layout Controls */
.layout-controls {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.layout-controls h5 {
    margin: 0 0 15px 0;
    color: #495057;
}

/* Large Modal */
.large-modal {
    max-width: 900px;
    width: 90%;
}

/* Image Preview Enhancements */
.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
    padding: 15px;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    min-height: 120px;
    background: #f8f9fa;
}

.image-preview:empty::before {
    content: "📸 الصور المحملة ستظهر هنا";
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100px;
}

.preview-image {
    background-size: cover;
    background-position: center;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    position: relative;
    display: inline-block;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.preview-image:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-image:hover {
    background: #c82333;
    transform: scale(1.1);
}

.content-section {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.content-section.active {
    display: block;
    opacity: 1;
}

.content-section h2 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
}

/* View More Link Styles */
.view-more-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 8px;
    background-color: #3b82f6;
    color: white;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.view-more-link:hover {
    background-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid #e2e8f0;
    position: relative;
}

.content-section h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stats Grid and Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: #ffffff;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid #f1f5f9;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleY(1);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #e2e8f0;
}

.stat-card:active {
    transform: translateY(-2px);
}

.stat-card i {
    font-size: 2.5rem;
    margin-left: 25px;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-info p {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

/* Settings Section Styles */
#settings.content-section {
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.settings-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 35px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #f1f5f9;
    position: relative;
    overflow: hidden;
}

.settings-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.settings-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.settings-card h3 {
    margin-bottom: 30px;
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: right;
    position: relative;
    padding-bottom: 15px;
}

.settings-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

@media (min-width: 768px) {
    .form-group {
        display: grid;
        grid-template-columns: 200px 1fr;
        align-items: start;
        gap: 25px;
        margin-bottom: 30px;
    }
}

.form-group label {
    display: block;
    color: #374151;
    font-weight: 600;
    font-size: 0.95rem;
    text-align: right;
    margin-bottom: 0;
    padding-top: 12px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #ffffff;
    font-family: 'Noto Sans Arabic', sans-serif;
    text-align: right;
    outline: none;
    color: #1f2937;
}

.form-group input:hover,
.form-group textarea:hover,
.form-group select:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background-color: #ffffff;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

.form-group input[type="file"] {
    padding: 12px;
    cursor: pointer;
    border-style: dashed;
    background-color: #f9fafb;
}

.form-group input[type="file"]::-webkit-file-upload-button {
    margin-left: 12px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Noto Sans Arabic', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-group input[type="file"]::-webkit-file-upload-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Enhanced product selection dropdown */
#productSelect {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-left: 40px;
    position: relative;
}

#productSelect option[disabled] {
    color: #9ca3af;
    font-style: italic;
    background-color: #f3f4f6;
}

#productSelect:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Product selection status indicator */
.product-selection-status {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    display: none;
}

.product-selection-status.active {
    display: block;
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.product-selection-status.inactive {
    display: block;
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Action Buttons */
.action-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    font-family: 'Noto Sans Arabic', sans-serif;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-button:hover::before {
    opacity: 1;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.action-button i,
.action-button span {
    position: relative;
    z-index: 1;
}

.action-button i {
    font-size: 1.1rem;
}

/* Enhanced toggle status button */
.toggle-status-btn {
    min-width: 90px;
    justify-content: center;
    position: relative;
    padding: 8px 12px;
}

.toggle-status-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.toggle-status-btn .status-text {
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 5px;
}

.toggle-status-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Settings specific button styles */
.settings-card .action-button {
    width: 100%;
    max-width: 250px;
    margin: 20px auto 0;
}

/* Image Placeholder Styles */
.image-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 1.5rem;
    border: 2px dashed #d1d5db;
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.product-image:error,
.product-image[src=""],
.product-image[src*="default-"] {
    display: none;
}

.product-image:error + .image-placeholder,
.product-image[src=""] + .image-placeholder,
.product-image[src*="default-"] + .image-placeholder {
    display: flex;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-content {
        margin-right: 0;
        padding: 15px;
        padding-top: 80px; /* Account for mobile menu toggle */
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stat-card {
        padding: 25px;
    }

    .stat-card i {
        font-size: 2rem;
        padding: 15px;
        margin-left: 20px;
    }

    .stat-info p {
        font-size: 1.75rem;
    }

    #settings.content-section {
        gap: 25px;
    }

    .settings-card {
        padding: 25px 20px;
        margin: 0;
        border-radius: 12px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 20px;
    }

    .form-group label {
        padding-top: 0;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    /* Enhanced mobile form elements */
    .form-group input,
    .form-group textarea,
    .form-group select {
        min-height: 48px;
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 14px 16px;
        border-radius: 8px;
        border: 2px solid #e5e7eb;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        touch-action: manipulation;
    }

    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        border-color: #3b82f6;
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }

    /* Enhanced button styling for mobile */
    .btn, button {
        min-height: 48px;
        min-width: 48px;
        font-size: 1rem;
        padding: 14px 20px;
        border-radius: 8px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        transition: all 0.2s ease;
    }

    .btn:active, button:active {
        transform: scale(0.98);
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
        border: none;
    }

    .btn-primary:hover, .btn-primary:focus {
        background: #2563eb;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
        border: none;
    }

    .btn-secondary:hover, .btn-secondary:focus {
        background: #4b5563;
    }

    .btn-danger {
        background: #ef4444;
        color: white;
        border: none;
    }

    .btn-danger:hover, .btn-danger:focus {
        background: #dc2626;
    }
        text-align: right;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 12px 14px;
        font-size: 1rem;
    }

    .settings-card .action-button {
        padding: 14px 24px;
        font-size: 1rem;
        max-width: 100%;
    }

    .content-section h2 {
        font-size: 1.75rem;
        margin-bottom: 25px;
    }
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-top: 30px;
    border: 1px solid #f1f5f9;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 18px 20px;
    text-align: right;
    border-bottom: 1px solid #f1f5f9;
}

th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

td {
    color: #6b7280;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

tbody tr {
    transition: all 0.2s ease;
}

tbody tr:hover {
    background-color: #f9fafb;
    transform: translateX(-2px);
}

tbody tr:hover td {
    color: #374151;
}

tbody tr:last-child td {
    border-bottom: none;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal[style*="display: block"],
.modal.modal-open {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.modal-content {
    background: #ffffff;
    width: 90%;
    max-width: 800px;
    margin: 40px auto;
    padding: 40px;
    border-radius: 20px;
    position: relative;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #f1f5f9;
    animation: modalSlideIn 0.3s ease-out;
}

.product-modal {
    margin: 2% auto;
    max-height: 90vh;
    overflow-y: auto;
}

.product-modal::-webkit-scrollbar {
    width: 8px;
}

.product-modal::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.product-modal::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.product-form .field-group {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    display: none;
}

#landingPageFields {
    border: none;
    background: transparent;
    padding: 0;
}

#landingPageFields h4 {
    font-size: 1.2em;
    color: #2196f3;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-label:hover {
    background-color: #f0f0f0;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.content-block {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 15px 0;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 1em;
}

.remove-block {
    background: #ff5252;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.remove-block:hover {
    background: #ff1744;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.preview-image {
    position: relative;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-image:hover {
    background: rgba(255, 82, 82, 0.9);
    color: white;
}

/* Share Buttons */
.share-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.share-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.share-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.share-button.facebook {
    background: #1877f2;
}

.share-button.whatsapp {
    background: #25d366;
}

.share-button.twitter {
    background: #1da1f2;
}

.share-button.copy {
    background: #6c757d;
}

.share-button.copy.copied {
    background: #28a745;
}

.view-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.view-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    background: #2563eb;
}

/* Landing Pages Table */
#landingPagesTable .preview-cell {
    width: 100px;
}

#landingPagesTable .preview-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
}

#landingPagesTable .actions-cell {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.edit-button, .delete-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-button {
    background: #fbbf24;
    color: white;
}

.delete-button {
    background: #ef4444;
    color: white;
}

.edit-button:hover, .delete-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    cursor: pointer;
    color: #ff5252;
    transition: all 0.2s;
}

.remove-image:hover {
    background: #ff5252;
    color: white;
}

.share-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.share-button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    transition: opacity 0.2s;
    font-size: 0.9em;
}

.share-button:hover {
    opacity: 0.9;
}

.share-button.facebook {
    background-color: #1877f2;
}

.share-button.twitter {
    background-color: #1da1f2;
}

.share-button.whatsapp {
    background-color: #25d366;
}

.share-button.copy {
    background-color: #6c757d;
}

.share-button i {
    font-size: 1.1em;
}

.share-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.share-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 4px;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
    min-width: 100px;
    justify-content: center;
}

.share-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.share-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.product-form .field-group.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.product-type-select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    margin-bottom: 20px;
    background-color: white;
    transition: all 0.3s ease;
}

.product-type-select:hover {
    border-color: #4a90e2;
}

.product-type-select:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33,150,243,0.1);
    outline: none;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #9ca3af;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f3f4f6;
}

.close:hover {
    color: #ef4444;
    background: #fee2e2;
    transform: scale(1.1);
}

.modal-content h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: right;
    padding-right: 60px;
}

/* Styles des formulaires */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
}

.form-group .help-text {
    font-size: 0.85em;
    color: #666;
    font-weight: normal;
}

.form-group .field-help {
    display: block;
    font-size: 0.8em;
    color: #888;
    margin-top: 4px;
    font-style: italic;
    line-height: 1.3;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.product-type-select {
    margin-bottom: 15px;
}

.field-group {
    display: none;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.field-group.active {
    display: block;
    opacity: 1;
}

.field-group h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f2f5;
}

.field-group .checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.field-group .checkbox-label input[type="checkbox"] {
    width: auto;
    margin-left: 10px;
}

.content-block {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
}

.remove-block {
    background: #e74c3c;
    color: #fff;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.remove-block:hover {
    background: #c0392b;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.preview-image {
    position: relative;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(231, 76, 60, 0.8);
    color: #fff;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.remove-image:hover {
    background: rgba(192, 57, 43, 0.9);
}

/* Status badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    display: inline-block;
}

.status-active {
    background-color: #27ae60;
    color: white;
}

.status-inactive {
    background-color: #95a5a6;
    color: white;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    width: 300px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease-out;
    transform: translateX(0);
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    flex: 1;
    margin-left: 10px;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.notification-message {
    color: #666;
    font-size: 0.9em;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    font-size: 1.2em;
    line-height: 1;
}

.notification-close:hover {
    color: #666;
}

.notification.new-order {
    border-right: 4px solid #3498db;
}

.notification.unread {
    background-color: #f8f9fa;
}

/* Notification types */
.notification.success {
    border-right: 4px solid #28a745;
    background-color: #d4edda;
}

.notification.success .notification-title {
    color: #155724;
}

.notification.error {
    border-right: 4px solid #dc3545;
    background-color: #f8d7da;
}

.notification.error .notification-title {
    color: #721c24;
}

.notification.info {
    border-right: 4px solid #17a2b8;
    background-color: #d1ecf1;
}

.notification.info .notification-title {
    color: #0c5460;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.status-paid {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #22c55e;
}

.status-shipped {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.orders-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: #475569;
}

.filter-group select {
    padding: 6px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
}

.orders-table {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.orders-table th {
    background-color: #f8fafc;
    color: #475569;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.05em;
}

.orders-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
}

.customer-info,
.order-details,
.date-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.customer-info strong,
.order-details strong,
.date-info strong {
    color: #1e293b;
    font-size: 0.95em;
}

.customer-email,
.items-count,
.time {
    color: #64748b;
    font-size: 0.85em;
}

.action-button {
    padding: 8px;
    margin: 0 4px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.action-button i {
    font-size: 1.1em;
}

.status-select {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.status-select:hover {
    border-color: #cbd5e1;
}

.status-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.orders-table tr:last-child td {
    border-bottom: none;
}

.orders-table tr {
    transition: all 0.2s ease;
}

.orders-table tr:hover {
    background-color: #f1f5f9;
    transform: translateX(-4px);
}

.orders-table tr:active {
    background-color: #e2e8f0;
    transform: translateX(0);
}

/* Styles des filtres */
.orders-filters {
    margin-bottom: 20px;
}

.orders-filters select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Noto Sans Arabic', sans-serif;
}



/* Bulk Selection Styles */
.section-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.bulk-controls {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.bulk-controls label {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
}

.product-checkbox,
.landing-page-checkbox {
    transform: scale(1.2);
    margin: 0;
    cursor: pointer;
}

.landing-page-card .landing-page-checkbox {
    margin-top: 5px;
}

/* Selected count styling */
#selectedProductsCount,
#selectedLandingPagesCount {
    font-weight: bold;
    color: #fff;
}

/* Bulk delete button styling */
#deleteSelectedProductsBtn,
#deleteSelectedLandingPagesBtn {
    transition: all 0.3s ease;
}

#deleteSelectedProductsBtn:hover,
#deleteSelectedLandingPagesBtn:hover {
    background: #c82333 !important;
    transform: translateY(-1px);
}

#deleteSelectedProductsBtn:disabled,
#deleteSelectedLandingPagesBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        right: -250px;
        transition: right 0.3s ease;
        z-index: 1000;
    }

    .sidebar.active {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
        padding: 20px;
    }
}

/* Enhanced AI Settings Styles */

/* Header Section */
.ai-providers-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    line-height: 1.5;
}

.providers-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    min-width: 50px;
}

/* Providers Grid */
.providers-grid-container {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.providers-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.grid-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cost-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
}

.legend-item.budget i { color: #28a745; }
.legend-item.balanced i { color: #ffc107; }
.legend-item.premium i { color: #dc3545; }

/* Enhanced Provider Cards */
.ai-provider-card.enhanced-card {
    border: 2px solid #e9ecef;
    border-radius: 16px;
    margin-bottom: 2rem;
    background: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.ai-provider-card.enhanced-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
}

.provider-info {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.provider-logo {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.openai-logo { background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%); }
.anthropic-logo { background: linear-gradient(135deg, #d97706 0%, #b45309 100%); }
.gemini-logo { background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%); }

.provider-details {
    flex: 1;
}

.provider-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.provider-description {
    font-size: 0.95rem;
    color: #6c757d;
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.provider-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.provider-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
}

.provider-pricing {
    text-align: right;
}

.pricing-info {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* Enhanced Configuration Section */
.provider-config-body {
    padding: 2rem;
    background: #fff;
}

.config-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.config-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.config-item {
    display: flex;
    flex-direction: column;
}

.config-item.enable-section {
    grid-column: 1 / -1;
}

.config-item.test-section {
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

/* Enhanced Form Elements */
.enhanced-checkbox {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.enhanced-checkbox:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.enhanced-checkbox .form-check-input:checked ~ .form-check-label {
    color: #667eea;
}

.checkbox-text {
    font-size: 1.1rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.checkbox-description {
    font-size: 0.9rem;
    color: #6c757d;
    display: block;
}

.enhanced-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-label i {
    color: #667eea;
}

.label-hint {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 400;
    margin-right: auto;
}

.enhanced-input {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
}

.enhanced-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.input-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.5rem;
    font-style: italic;
}

.enhanced-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
}

.enhanced-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.enhanced-select optgroup {
    font-weight: 600;
    color: #2c3e50;
    padding: 0.5rem 0;
}

.enhanced-select option {
    padding: 0.5rem;
    font-size: 0.9rem;
}

/* Model Information Display */
.model-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.75rem;
    transition: all 0.3s ease;
}

.model-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

.model-cost-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.model-cost-badge.budget {
    background: #d4edda;
    color: #155724;
}

.model-cost-badge.balanced {
    background: #fff3cd;
    color: #856404;
}

.model-cost-badge.premium {
    background: #f8d7da;
    color: #721c24;
}

/* Enhanced Test Button */
.enhanced-test-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 140px;
}

.enhanced-test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.enhanced-test-btn:active {
    transform: translateY(0);
}

.enhanced-test-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-text {
    font-size: 0.95rem;
}

.btn-description {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Responsive Design for AI Settings */
@media (max-width: 768px) {
    .ai-providers-header {
        padding: 1.5rem;
        text-align: center;
    }

    .section-title-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .providers-summary {
        justify-content: center;
        gap: 1rem;
    }

    .providers-grid-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .cost-legend {
        justify-content: center;
    }

    .provider-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .provider-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .provider-status {
        align-items: center;
    }

    .config-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .config-item.enable-section {
        grid-column: 1;
    }

    .enhanced-test-btn {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .providers-grid-container {
        padding: 1rem;
    }

    .provider-config-body {
        padding: 1rem;
    }

    .ai-providers-header {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .provider-name {
        font-size: 1.1rem;
    }

    .provider-features {
        justify-content: center;
    }
}

/* Status Badge Enhancements */
.ai-status-enabled {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ai-status-disabled {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Enhanced Hover Effects */
.enhanced-input:hover {
    border-color: #b8c5f2;
}

.enhanced-select:hover {
    border-color: #b8c5f2;
}

.enhanced-checkbox:has(.form-check-input:checked) {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f4ff 0%, #e8f0fe 100%);
}

/* Loading States */
.enhanced-test-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.enhanced-test-btn.loading .btn-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Enhanced Admin Pages Styles */

/* Admin Content Containers - Remove excessive spacing */
.general-settings-content,
.user-management-content,
.security-settings-content,
.ai-settings-content,
.payment-settings-content,
.categories-management-content {
    margin: 0;
    padding: 0;
}

/* Header Styles for All Admin Sections */
.general-settings-header,
.user-management-header,
.security-settings-header,
.ai-settings-header,
.payment-settings-header,
.categories-management-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    margin: 0 0 2rem 0; /* Remove top margin, keep bottom margin */
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.settings-summary,
.users-summary,
.security-summary,
.ai-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.2rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    min-width: 60px;
}

.summary-value.status-active {
    background: rgba(40, 167, 69, 0.3);
    color: #d4edda;
}

.summary-value.security-level {
    background: rgba(255, 193, 7, 0.3);
    color: #fff3cd;
}

/* Settings Grid */
.settings-grid-container,
.security-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.settings-card.enhanced-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.settings-card.enhanced-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.store-icon { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.business-icon { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); }
.system-icon { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }
.seo-icon { background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%); }
.auth-icon { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
.access-icon { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
.data-icon { background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%); }
.monitor-icon { background: linear-gradient(135deg, #20c997 0%, #28a745 100%); }
.ssl-icon { background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%); }
.audit-icon { background: linear-gradient(135deg, #343a40 0%, #6c757d 100%); }

.card-title-section {
    flex: 1;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.card-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

.card-body {
    padding: 2rem;
}

/* Enhanced Forms */
.enhanced-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.enhanced-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-label i {
    color: #667eea;
    width: 16px;
}

.enhanced-input,
.enhanced-select,
.enhanced-textarea {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
    font-family: inherit;
}

.enhanced-input:focus,
.enhanced-select:focus,
.enhanced-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.enhanced-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Action Buttons */
.settings-actions,
.security-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    padding: 2rem 0;
    border-top: 2px solid #e9ecef;
    margin-top: 2rem;
}

.enhanced-save-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.enhanced-reset-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.enhanced-export-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* User Management Styles */
.users-tools-container {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.tools-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.tools-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tools-actions {
    display: flex;
    gap: 1rem;
}

.enhanced-btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
}

.enhanced-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.users-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.filter-label i {
    color: #667eea;
    width: 14px;
}

/* Enhanced Table */
.users-table-container {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
}

.table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.table-info {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.enhanced-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.enhanced-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
}

.enhanced-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.enhanced-table tbody tr:hover {
    background: #f8f9fa;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-numbers button {
    width: 32px;
    height: 32px;
    border: 1px solid #e9ecef;
    background: #fff;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-numbers button:hover,
.page-numbers button.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Bulk Actions */
.bulk-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 1000;
}

.bulk-actions-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
}

.bulk-count {
    font-weight: 600;
    color: #2c3e50;
}

.bulk-buttons {
    display: flex;
    gap: 0.5rem;
}

.bulk-buttons .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: 6px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: #fff;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* Security Settings Specific Styles */
.ssl-status {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.status-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
}

.status-label {
    font-weight: 600;
    color: #2c3e50;
}

.status-value {
    font-weight: 500;
    color: #6c757d;
}

.audit-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.audit-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 200px;
}

.audit-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
}

.audit-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.enhanced-test-btn {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}



/* Reports and Analytics Styles */
.reports-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.summary-card.sales .card-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.summary-card.orders .card-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.summary-card.customers .card-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.summary-card.products .card-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.card-content h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.card-value {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
}

.card-change {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
}

.card-change.positive {
    background: #dcfce7;
    color: #166534;
}

.card-change.negative {
    background: #fef2f2;
    color: #dc2626;
}

.card-change.neutral {
    background: #f3f4f6;
    color: #6b7280;
}

.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    margin: 0;
    font-size: 18px;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-controls select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.chart-content {
    position: relative;
    height: 200px;
}

.reports-table-container,
.activity-container {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.table-header,
.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3,
.activity-header h3 {
    margin: 0;
    font-size: 18px;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reports-table {
    width: 100%;
    border-collapse: collapse;
}

.reports-table th,
.reports-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

.reports-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.rank {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
}

.progress-bar {
    position: relative;
    width: 100px;
    height: 20px;
    background: #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #374151;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.activity-icon.order { background: #3b82f6; }
.activity-icon.product { background: #10b981; }
.activity-icon.customer { background: #8b5cf6; }
.activity-icon.payment { background: #f59e0b; }
.activity-icon.review { background: #ef4444; }

.activity-content {
    flex: 1;
}

.activity-message {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #374151;
}

.activity-time {
    font-size: 12px;
    color: #6b7280;
}

.export-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.export-section h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #1f2937;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.export-options {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .export-options {
        flex-direction: column;
        align-items: center;
    }

    .export-options .action-button {
        width: 100%;
        max-width: 200px;
    }
}

/* Additional spacing fixes for admin content */
.admin-content,
.content-section {
    margin: 0;
    padding: 0;
}

/* Remove any excessive top margins from first elements */
.general-settings-content > *:first-child,
.user-management-content > *:first-child,
.security-settings-content > *:first-child,
.ai-settings-content > *:first-child,
.payment-settings-content > *:first-child,
.categories-management-content > *:first-child {
    margin-top: 0 !important;
}

/* Categories Tree Specific Styles */
.categories-tree-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
}

.tree-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-categories {
    position: relative;
    margin-right: auto;
}

.search-categories input {
    padding-left: 2.5rem;
    width: 250px;
}

.search-categories i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
}

.categories-tree {
    min-height: 200px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: #f9fafb;
}

.tree-loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

/* Responsive Design for Admin Pages */
@media (max-width: 768px) {
    .general-settings-header,
    .user-management-header,
    .security-settings-header,
    .ai-settings-header,
    .payment-settings-header,
    .categories-management-header {
        padding: 1.5rem;
        text-align: center;
    }

    .section-title-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .settings-summary,
    .users-summary,
    .security-summary {
        justify-content: center;
        gap: 1rem;
    }

    .settings-grid-container,
    .security-grid-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .users-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .tools-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .tools-actions {
        justify-content: center;
    }

    .table-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .bulk-actions {
        position: relative;
        bottom: auto;
        right: auto;
        margin-top: 1rem;
    }

    .bulk-actions-content {
        flex-direction: column;
        text-align: center;
    }

    .settings-actions,
    .security-actions {
        flex-direction: column;
        align-items: center;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 1.3rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .enhanced-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .audit-controls {
        flex-direction: column;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #fff;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #007bff;
    z-index: 3000;
    display: flex;
    align-items: center;
    gap: 1rem;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    border-left-color: #28a745;
    color: #155724;
}

.notification-error {
    border-left-color: #dc3545;
    color: #721c24;
}

.notification-warning {
    border-left-color: #ffc107;
    color: #856404;
}

.notification-info {
    border-left-color: #17a2b8;
    color: #0c5460;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Alert Animations for AI Settings */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Enhanced Alert Styles for AI Settings */
.ai-alert {
    border-left: 4px solid;
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.ai-alert.alert-success {
    border-left-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.ai-alert.alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

.ai-alert .btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-alert .btn-close:hover {
    opacity: 1;
}

.ai-alert .btn-close::before {
    content: '×';
    font-weight: bold;
}

/* Store Settings Styles */
.store-settings-container {
    max-width: 1000px;
    margin: 0 auto;
}

.settings-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.settings-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.settings-header p {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.settings-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    position: relative;
    overflow: hidden;
}

.settings-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.settings-section h3 {
    color: #1e293b;
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 0 25px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f5f9;
}

.settings-section h3 i {
    color: #667eea;
    font-size: 1.2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.store-settings-container .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.store-settings-container .form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.store-settings-container .form-group label i {
    color: #667eea;
    font-size: 0.9rem;
}

.store-settings-container .form-group input,
.store-settings-container .form-group textarea,
.store-settings-container .form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Noto Sans Arabic', sans-serif;
    transition: all 0.3s ease;
    background: white;
    color: #1f2937;
    text-align: right;
}

.store-settings-container .form-group input:focus,
.store-settings-container .form-group textarea:focus,
.store-settings-container .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.store-settings-container .form-group textarea {
    min-height: 80px;
    resize: vertical;
    line-height: 1.5;
}

/* Logo Upload Styles */
.logo-upload-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.logo-preview {
    width: 120px;
    height: 120px;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
    color: #6b7280;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.logo-preview:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.logo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.logo-preview i {
    font-size: 2rem;
    margin-bottom: 8px;
    color: #9ca3af;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    align-self: flex-start;
}

.upload-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 30px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.save-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.save-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
}

.save-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.reset-btn {
    background: #6b7280;
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.reset-btn:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

/* Error Message Styles */
.error-message {
    text-align: center;
    padding: 40px;
    color: #dc2626;
}

.error-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.retry-btn {
    background: #dc2626;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #b91c1c;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .settings-section {
        padding: 20px;
    }

    .settings-header {
        padding: 20px;
    }

    .settings-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 10px;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .save-btn,
    .reset-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* Categories Management Styles */
.categories-management-content {
    max-width: 1200px;
    margin: 0 auto;
}

.categories-management-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.section-title-content .section-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title-content .section-subtitle {
    font-size: 1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    line-height: 1.5;
}

.settings-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    min-width: 50px;
}

/* Categories Tree Styles */
.categories-tree-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
}

.tree-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-categories {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-categories input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-categories input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-categories i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

/* Category Cards */
.category-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
}

.category-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.category-info h3 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.2rem;
    font-weight: 600;
}

.category-info p {
    margin: 0 0 0.5rem 0;
    color: #64748b;
    font-size: 0.9rem;
}

.category-info small {
    color: #9ca3af;
    font-size: 0.8rem;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-toggle {
    background: #10b981;
    color: white;
}

.btn-toggle.inactive {
    background: #6b7280;
}

.btn-edit {
    background: #f59e0b;
    color: white;
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Loading and Error States */
.tree-loading,
.tree-error {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.tree-loading i,
.tree-error i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.tree-error {
    color: #dc2626;
}

.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Icon Selector */
.icon-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: #f9fafb;
}

.icon-option {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.icon-option:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.icon-option.selected {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.icon-option i {
    font-size: 1.2rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .categories-management-header {
        padding: 1.5rem;
        text-align: center;
    }

    .section-title-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .section-title-content .section-title {
        font-size: 1.5rem;
    }

    .settings-summary {
        justify-content: center;
        gap: 1rem;
    }

    .tree-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-categories {
        max-width: none;
    }

    .category-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .category-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .icon-selector {
        grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    }

    .icon-option {
        width: 40px;
        height: 40px;
    }
}

/* Enhanced Payment Settings Styles */
.payment-settings-container {
    max-width: 1200px;
    margin: 0 auto;
}

.payment-settings-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
}

.payment-settings-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.payment-settings-header p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Modern Settings Design */
.settings-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.settings-header h2 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.settings-header h2 i {
    color: #667eea;
    margin-left: 15px;
}

.settings-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
    font-weight: 400;
}

.settings-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
    padding: 0 10px;
}

.setting-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.setting-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.setting-card:hover::before {
    transform: scaleY(1);
}

.setting-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.setting-card-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
    border-radius: inherit;
}

.setting-card-icon.categories {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.setting-card-icon.payment {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.setting-card-icon.general {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.setting-card-icon.users {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

.setting-card-icon.roles {
    background: linear-gradient(135deg, #28a745 0%, #17a2b8 100%);
}

.setting-card-icon.subscriptions {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.setting-card-icon.security {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.setting-card-icon.testing {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.setting-card-content {
    flex: 1;
    text-align: right;
}

.setting-card-content h3 {
    margin: 0 0 8px 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
}

.setting-card-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.4;
}

.setting-card-arrow {
    color: #adb5bd;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.setting-card:hover .setting-card-arrow {
    color: #667eea;
    transform: translateX(-3px);
}

/* System Testing Styles */
.system-testing-content {
    max-width: 1000px;
    margin: 0 auto;
}

.testing-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 16px;
}

.testing-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
}

.testing-header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.testing-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.testing-results {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.test-results-summary {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.test-summary-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px 20px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item.success {
    border-left: 4px solid #28a745;
}

.stat-item.warning {
    border-left: 4px solid #ffc107;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

.test-results-details {
    display: grid;
    gap: 12px;
}

.test-result-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    gap: 12px;
    transition: all 0.3s ease;
}

.test-result-item.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.test-result-item.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.test-result-item:hover {
    transform: translateX(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.test-name {
    font-weight: 600;
    flex: 1;
}

.test-message {
    font-size: 0.9rem;
    opacity: 0.8;
}

.setting-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    overflow: hidden;
    transition: all 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.setting-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.setting-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-left: 1rem;
}

.setting-info {
    flex: 1;
}

.setting-info h5 {
    margin: 0 0 0.25rem 0;
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
}

.setting-info p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
}

.setting-toggle {
    margin-right: 1rem;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #10b981;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.setting-content {
    padding: 1.5rem;
    display: none;
}

.setting-card[data-method] .setting-content {
    display: block;
}

.setting-card[data-method="cod"] .setting-content,
.setting-card[data-method="ccp"] .setting-content[style*="display: none"],
.setting-card[data-method="baridimob"] .setting-content[style*="display: none"] {
    display: none;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.enhanced-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.enhanced-label i {
    color: #667eea;
    font-size: 0.85rem;
}

.enhanced-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Noto Sans Arabic', sans-serif;
    transition: all 0.3s ease;
    background: white;
    color: #1f2937;
}

.enhanced-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-error {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.field-error::before {
    content: '⚠️';
    font-size: 0.7rem;
}

.test-payment-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.test-payment-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.test-payment-btn.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.test-payment-btn.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.test-payment-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.form-text {
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.status-active {
    color: #10b981 !important;
}

.status-inactive {
    color: #ef4444 !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .settings-grid-modern {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 5px;
    }

    .setting-card {
        padding: 20px;
        gap: 15px;
    }

    .setting-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .setting-card-content h3 {
        font-size: 1.2rem;
    }

    .settings-header h2 {
        font-size: 2rem;
    }
}

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .setting-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .setting-icon {
        margin: 0;
    }

    .payment-settings-header {
        padding: 1.5rem;
        text-align: center;
    }

    .payment-settings-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Enhanced User Management Styles */
.user-management-container {
    max-width: 1400px;
    margin: 0 auto;
}

.user-management-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.user-management-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-management-header p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}

/* User Avatar Styles */
.user-avatar-container {
    position: relative;
    display: inline-block;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.user-status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.user-status-indicator.active {
    background-color: #10b981;
}

.user-status-indicator.inactive {
    background-color: #6b7280;
}

.user-status-indicator.suspended {
    background-color: #ef4444;
}

/* User Info Styles */
.user-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.95rem;
}

.user-id {
    font-size: 0.8rem;
    color: #6b7280;
}

.user-phone {
    font-size: 0.8rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.user-email {
    font-weight: 500;
    color: #374151;
}

.user-store {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 500;
}

/* Role and Subscription Styles */
.role-container,
.subscription-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.badge-subscription {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.subscription-details {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.3;
}

/* Date Info Styles */
.date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.registered-date {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.last-login {
    font-size: 0.8rem;
    color: #6b7280;
}

/* Subscription Modal Styles */
.subscription-form {
    padding: 1.5rem;
}

.subscription-form .form-group {
    margin-bottom: 1.5rem;
}

.subscription-form label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.subscription-form select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    color: #1f2937;
}

.subscription-form select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.user-name-display {
    font-weight: 600;
    color: #1f2937;
    font-size: 1.1rem;
}

.subscription-preview {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.limits-preview h4 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1rem;
}

.limits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.limit-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.limit-item i {
    color: #667eea;
    font-size: 1rem;
}

.limit-item span {
    font-size: 0.9rem;
    color: #374151;
    font-weight: 500;
}

/* Bulk Actions Styles */
.bulk-actions-container {
    background: #f0f4ff;
    border: 1px solid #c7d2fe;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions-container.show {
    display: block;
}

.bulk-actions-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.selected-count {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.bulk-actions-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bulk-action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bulk-action-btn.activate {
    background: #10b981;
    color: white;
}

.bulk-action-btn.deactivate {
    background: #6b7280;
    color: white;
}

.bulk-action-btn.delete {
    background: #ef4444;
    color: white;
}

.bulk-action-btn.export {
    background: #f59e0b;
    color: white;
}

.bulk-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Enhanced Table Styles */
.users-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.users-table th {
    background: #f8fafc;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

.users-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.user-row:hover {
    background: #f8fafc;
}

.user-row.selected {
    background: #f0f4ff;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.action-buttons .btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-info {
    background: #06b6d4;
    color: white;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

/* Retry Button */
.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .user-management-header {
        padding: 1.5rem;
        text-align: center;
    }

    .user-management-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .limits-grid {
        grid-template-columns: 1fr;
    }

    .bulk-actions-buttons {
        justify-content: center;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .users-table {
        font-size: 0.9rem;
    }

    .users-table th,
    .users-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Role Management Styles */
.roles-management-content {
    padding: 20px;
    background: #f8fafc;
    min-height: 100vh;
}

.roles-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.roles-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.roles-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.roles-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.roles-actions .btn {
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.roles-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.roles-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.roles-actions .btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.roles-actions .btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.roles-actions .btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

.roles-list {
    display: grid;
    gap: 20px;
}

.role-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 15px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.role-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px 16px 0 0;
}

.role-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-top: 5px;
}

.role-info h3 {
    color: #1e293b;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.role-info p {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.role-level {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.role-actions {
    display: flex;
    gap: 8px;
}

.role-stats {
    display: flex;
    gap: 20px;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
}

.role-stats .stat {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #64748b;
}

.role-stats .stat.active {
    color: #10b981;
}

.role-stats .stat.inactive {
    color: #ef4444;
}

.role-stats .stat i {
    font-size: 0.8rem;
}

/* Subscription Management Styles */
.subscription-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.subscription-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.subscription-info h3 {
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.subscription-info p {
    color: #64748b;
    font-size: 0.95rem;
    margin-bottom: 12px;
}

.subscription-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #10b981;
}

.subscription-price .duration {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: normal;
}

.subscription-limits {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
}

.subscription-limits h4 {
    color: #1e293b;
    font-size: 1rem;
    margin-bottom: 12px;
}

.limits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.limit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #64748b;
}

.limit-item i {
    color: #667eea;
    width: 16px;
}

.subscription-stats {
    display: flex;
    gap: 20px;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
}

.subscription-stats .stat {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #64748b;
}

.subscription-stats .stat.active {
    color: #10b981;
}

.subscription-stats .stat.inactive {
    color: #ef4444;
}

.subscription-stats .stat i {
    font-size: 0.8rem;
}

/* Store Settings Styles */
.store-settings-content {
    padding: 20px;
}

.store-settings-header {
    margin-bottom: 30px;
    text-align: center;
}

.store-settings-header h1 {
    color: #1e293b;
    font-size: 2rem;
    margin-bottom: 10px;
}

.store-settings-header p {
    color: #64748b;
    font-size: 1.1rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.setting-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.setting-section h3 {
    color: #1e293b;
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.setting-section h3 i {
    color: #667eea;
}

.setting-section .form-group {
    margin-bottom: 20px;
}

.setting-section .form-group:last-child {
    margin-bottom: 0;
}

.setting-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.setting-section input,
.setting-section select,
.setting-section textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.setting-section input:focus,
.setting-section select:focus,
.setting-section textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.settings-actions {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.settings-actions .btn {
    margin: 0 10px;
    min-width: 150px;
}

/* Store Settings Icon Colors */
.setting-card-icon.store {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Role Modal Styles */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.permission-item:hover {
    background: #e2e8f0;
}

.permission-item input[type="checkbox"] {
    margin: 0;
    width: auto;
}

.permission-item span {
    font-size: 0.9rem;
    color: #374151;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Subscription Modal Styles */
.limits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.limit-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.limit-item label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0;
}

.limit-item input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.limit-item input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* Form Validation Styles */
.enhanced-input.error,
.enhanced-select.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.enhanced-input.success,
.enhanced-select.success {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.field-error {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.field-error i {
    font-size: 0.7rem;
}

.field-success {
    color: #10b981;
    font-size: 0.8rem;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.field-success i {
    font-size: 0.7rem;
}

/* Modal Loading State */
.modal-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .setting-section {
        padding: 20px;
    }

    .settings-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }
}
