<?php
/**
 * Create Sample Stores Script
 * Creates sample store data for testing the store management system
 */

require_once '../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🌱 إنشاء بيانات تجريبية للمتاجر...\n\n";
    
    // First, check if stores table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'stores'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        echo "❌ جدول المتاجر غير موجود. يرجى تشغيل ترحيل قاعدة البيانات أولاً.\n";
        exit(1);
    }
    
    // Check if users table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        echo "📝 إنشاء مستخدمين تجريبيين...\n";
        
        // Create sample users
        $sampleUsers = [
            [
                'nom' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'telephone' => '0555123456',
                'mot_de_passe' => password_hash('123456', PASSWORD_DEFAULT),
                'wilaya' => 'الجزائر',
                'commune' => 'الجزائر الوسطى'
            ],
            [
                'nom' => 'فاطمة علي',
                'email' => '<EMAIL>',
                'telephone' => '0666789012',
                'mot_de_passe' => password_hash('123456', PASSWORD_DEFAULT),
                'wilaya' => 'وهران',
                'commune' => 'وهران'
            ],
            [
                'nom' => 'محمد الأمين',
                'email' => '<EMAIL>',
                'telephone' => '0777345678',
                'mot_de_passe' => password_hash('123456', PASSWORD_DEFAULT),
                'wilaya' => 'قسنطينة',
                'commune' => 'قسنطينة'
            ]
        ];
        
        foreach ($sampleUsers as $user) {
            $stmt = $pdo->prepare("
                INSERT INTO users (nom, email, telephone, mot_de_passe, wilaya, commune, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $user['nom'],
                $user['email'],
                $user['telephone'],
                $user['mot_de_passe'],
                $user['wilaya'],
                $user['commune']
            ]);
            echo "✅ تم إنشاء المستخدم: {$user['nom']}\n";
        }
    }
    
    // Get user IDs
    $stmt = $pdo->query("SELECT id, nom FROM users LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "❌ لا توجد مستخدمين في قاعدة البيانات.\n";
        exit(1);
    }
    
    echo "\n🏪 إنشاء متاجر تجريبية...\n";
    
    // Sample stores data
    $sampleStores = [
        [
            'store_name' => 'متجر الكتب الإلكترونية',
            'store_slug' => 'ebooks-store',
            'description' => 'متجر متخصص في بيع الكتب الإلكترونية والمواد التعليمية',
            'status' => 'active',
            'theme' => 'modern',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#667eea',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الإلكترونيات',
            'store_slug' => 'electronics-store',
            'description' => 'أحدث الأجهزة الإلكترونية والهواتف الذكية',
            'status' => 'active',
            'theme' => 'tech',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#4f46e5',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الأزياء النسائية',
            'store_slug' => 'fashion-store',
            'description' => 'أجمل الأزياء والإكسسوارات النسائية',
            'status' => 'pending',
            'theme' => 'fashion',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#ec4899',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر المنتجات الطبيعية',
            'store_slug' => 'natural-products',
            'description' => 'منتجات طبيعية وعضوية للصحة والجمال',
            'status' => 'suspended',
            'theme' => 'natural',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#10b981',
                'allow_reviews' => true
            ]
        ],
        [
            'store_name' => 'متجر الرياضة واللياقة',
            'store_slug' => 'sports-store',
            'description' => 'معدات رياضية ومكملات غذائية للرياضيين',
            'status' => 'active',
            'theme' => 'sports',
            'settings' => [
                'currency' => 'DZD',
                'language' => 'ar',
                'timezone' => 'Africa/Algiers',
                'theme_color' => '#f59e0b',
                'allow_reviews' => true
            ]
        ]
    ];
    
    // Clear existing sample stores
    $stmt = $pdo->prepare("DELETE FROM stores WHERE store_slug IN (?, ?, ?, ?, ?)");
    $stmt->execute(['ebooks-store', 'electronics-store', 'fashion-store', 'natural-products', 'sports-store']);
    
    // Create sample stores
    foreach ($sampleStores as $index => $store) {
        $userId = $users[$index % count($users)]['id'];
        $userName = $users[$index % count($users)]['nom'];
        
        $stmt = $pdo->prepare("
            INSERT INTO stores (
                user_id, store_name, store_slug, description, status, theme, settings,
                total_products, total_orders, total_revenue, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $userId,
            $store['store_name'],
            $store['store_slug'],
            $store['description'],
            $store['status'],
            $store['theme'],
            json_encode($store['settings']),
            rand(5, 50), // total_products
            rand(0, 100), // total_orders
            rand(1000, 50000) / 100 // total_revenue
        ]);
        
        echo "✅ تم إنشاء المتجر: {$store['store_name']} (المالك: {$userName})\n";
    }
    
    // Get final count
    $stmt = $pdo->query("SELECT COUNT(*) FROM stores");
    $storeCount = $stmt->fetchColumn();
    
    echo "\n📊 إحصائيات البيانات التجريبية:\n";
    echo "👥 عدد المستخدمين: " . count($users) . "\n";
    echo "🏪 عدد المتاجر: {$storeCount}\n";
    
    // Show stores by status
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as count 
        FROM stores 
        GROUP BY status
    ");
    $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📈 المتاجر حسب الحالة:\n";
    foreach ($statusCounts as $status) {
        echo "   {$status['status']}: {$status['count']}\n";
    }
    
    echo "\n🎉 تم إنشاء البيانات التجريبية بنجاح!\n";
    echo "\n💡 يمكنك الآن اختبار نظام إدارة المتاجر في لوحة التحكم.\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - وجود جدول المتاجر (تشغيل الترحيل)\n";
    echo "   - صحة الاتصال بقاعدة البيانات\n";
    echo "   - صلاحيات الكتابة في قاعدة البيانات\n";
}
?>
