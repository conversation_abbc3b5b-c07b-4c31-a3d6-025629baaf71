// Gestionnaire de notifications
const notificationManager = {
    showSuccess(message) {
        const notification = {
            id: Date.now(),
            type: 'success',
            message: message
        };
        this.showNotification(notification);
    },

    showError(message) {
        const notification = {
            id: Date.now(),
            type: 'error',
            message: message
        };
        this.showNotification(notification);
    },

    showInfo(message) {
        const notification = {
            id: Date.now(),
            type: 'info',
            message: message
        };
        this.showNotification(notification);
    },
    container: null,
    checkInterval: null,
    unreadCount: 0,

    init() {
        this.container = document.getElementById('notificationsContainer');
        this.startPolling();
    },

    startPolling() {
        this.checkNotifications();
        this.checkInterval = setInterval(() => this.checkNotifications(), 30000); // Vérifier toutes les 30 secondes
    },

    async checkNotifications() {
        try {
            const response = await fetch('../php/notifications.php?action=unread');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const text = await response.text();
            if (!text.trim()) {
                console.warn('Empty response from notifications API');
                return;
            }

            const notifications = JSON.parse(text);

            if (Array.isArray(notifications)) {
                // Mettre à jour le compteur
                this.unreadCount = notifications.length;
                this.updateBadge();

                // Afficher les nouvelles notifications
                notifications.forEach(notification => this.showNotification(notification));
            }
        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
        }
    },

    showNotification(notification) {
        const existingNotif = document.querySelector(`[data-notification-id="${notification.id}"]`);
        if (existingNotif) return;

        const notifElement = document.createElement('div');
        notifElement.className = `notification ${notification.type} unread`;
        notifElement.dataset.notificationId = notification.id;

        notifElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${this.getNotificationTitle(notification.type)}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.dismissNotification(${notification.id})">
                &times;
            </button>
        `;

        this.container.appendChild(notifElement);

        // Auto-dismiss success and info notifications after 5 seconds
        if (notification.type === 'success' || notification.type === 'info') {
            setTimeout(() => {
                this.dismissNotification(notification.id);
            }, 5000);
        }

        // Jouer un son de notification (with better error handling and fallback)
        this.playNotificationSound();
    },

    playNotificationSound() {
        // Use Web Audio API directly for better reliability
        try {
            this.playWebAudioNotification();
        } catch (error) {
            // Try MP3 as fallback
            try {
                const audio = new Audio('../assets/notification.mp3');
                audio.volume = 0.3;
                audio.preload = 'none';

                // Only try to play if user has interacted with the page
                if (document.hasFocus()) {
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.debug('All notification methods failed:', error.message);
                        });
                    }
                }
            } catch (audioError) {
                console.debug('All notification methods failed:', audioError.message);
            }
        }
    },

    playWebAudioNotification() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Create a pleasant notification beep
            oscillator.frequency.value = 800; // 800 Hz
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);

            console.debug('Web Audio notification played successfully');
        } catch (error) {
            // Completely silent fallback
            console.debug('All notification sound methods failed:', error.message);
        }
    },

    getNotificationTitle(type) {
        const titles = {
            'new_order': 'طلب جديد',
            'payment_received': 'تم استلام الدفع',
            'low_stock': 'تنبيه المخزون'
        };
        return titles[type] || 'إشعار';
    },

    updateBadge() {
        const ordersLink = document.querySelector('[data-section="orders"]');
        let badge = ordersLink.querySelector('.notification-badge');

        if (this.unreadCount > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                ordersLink.appendChild(badge);
            }
            badge.textContent = this.unreadCount;
        } else if (badge) {
            badge.remove();
        }
    },

    // Dismiss notification immediately (for local notifications)
    dismissNotification(id) {
        const notif = document.querySelector(`[data-notification-id="${id}"]`);
        if (notif) {
            notif.style.opacity = '0';
            notif.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notif.remove();
            }, 300);
        }
    },

    // Close notification and mark as read (for server notifications)
    async closeNotification(id) {
        try {
            await fetch(`../php/notifications.php?action=mark_read&id=${id}`);
            const notif = document.querySelector(`[data-notification-id="${id}"]`);
            if (notif) {
                notif.style.opacity = '0';
                notif.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notif.remove();
                }, 300);
                this.unreadCount = Math.max(0, this.unreadCount - 1);
                this.updateBadge();
            }
        } catch (error) {
            console.error('Erreur lors de la fermeture de la notification:', error);
            // Still remove the notification from UI even if server request fails
            this.dismissNotification(id);
        }
    },

    // Clear all notifications
    clearAllNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notif => {
            const id = notif.dataset.notificationId;
            this.dismissNotification(id);
        });
    }
};

// Vérifier l'authentification avec gestion d'erreur améliorée
function checkAuth() {
    fetch('../php/admin.php?action=check')
        .then(response => {
            console.log('Auth check response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            console.log('Auth check raw response:', text);

            if (!text.trim()) {
                console.warn('Empty response from auth check');
                window.location.href = 'login.html';
                return;
            }

            // Try to extract JSON from response (in case there are PHP warnings)
            let data;
            try {
                // Look for JSON in the response, even if there are PHP warnings before it
                const jsonMatch = text.match(/\{.*\}$/);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[0]);
                    if (text !== jsonMatch[0]) {
                        console.warn('⚠️ PHP warnings detected in auth response:', text.replace(jsonMatch[0], ''));
                    }
                } else {
                    data = JSON.parse(text);
                }
            } catch (parseError) {
                console.error('JSON parse error in auth check:', parseError);
                console.error('Response was:', text);
                // If it's not JSON, it might be an error page - redirect to login
                window.location.href = 'login.html';
                return;
            }

            if (!data.logged_in) {
                console.log('User not logged in, redirecting to login');
                window.location.href = 'login.html';
            } else {
                console.log('User authenticated successfully');
            }
        })
        .catch(error => {
            console.error('Auth check error:', error);
            // Only redirect to login if it's not a network error
            if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                console.error('Network error during auth check - server may be down');
                // Show a user-friendly message instead of immediate redirect
                if (confirm('خطأ في الاتصال بالخادم. هل تريد المحاولة مرة أخرى؟')) {
                    setTimeout(checkAuth, 2000); // Retry after 2 seconds
                } else {
                    window.location.href = 'login.html';
                }
            } else {
                window.location.href = 'login.html';
            }
        });
}

// Load settings data
async function loadSettings() {
    try {
        console.log('Loading settings...');
        const response = await fetch('../php/admin.php?action=get_store_settings');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('Settings response text:', responseText);

        if (!responseText.trim()) {
            console.warn('Empty response from settings API');
            return;
        }

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response was:', responseText.substring(0, 200));
            throw new Error('Invalid JSON response from settings API');
        }

        console.log('Parsed settings data:', data);

        // Handle different response formats
        let settings = {};
        if (data.success && data.settings) {
            // Format: {success: true, settings: {key: value}}
            settings = data.settings;
        } else if (data.settings) {
            // Format: {settings: {key: value}}
            settings = data.settings;
        } else {
            // Direct format: {key: value}
            settings = data;
        }

        console.log('Final settings object:', settings);

        // Fill form fields
        const storeNameField = document.getElementById('storeName');
        const storePhoneField = document.getElementById('storePhone');
        const storeEmailField = document.getElementById('storeEmail');
        const storeAddressField = document.getElementById('storeAddress');

        if (storeNameField) storeNameField.value = settings.store_name || '';
        if (storePhoneField) storePhoneField.value = settings.phone_number || settings.store_phone || '';
        if (storeEmailField) storeEmailField.value = settings.contact_email || settings.store_email || '';
        if (storeAddressField) storeAddressField.value = settings.address || settings.store_address || '';

        // Update TinyMCE for storeAddress field
        if (window.tinymce && storeAddressField) {
            const editor = tinymce.get('storeAddress');
            if (editor) {
                editor.setContent(settings.address || settings.store_address || '');
            }
        }

        console.log('Settings loaded successfully');
    } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // Don't show alert to user for settings, just log it
    }
}

// Handle store settings form submission
document.getElementById('storeSettingsForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    try {
        const formData = new FormData();
        formData.append('store_name', document.getElementById('storeName').value);
        formData.append('store_phone', document.getElementById('storePhone').value);
        formData.append('store_email', document.getElementById('storeEmail').value);
        formData.append('store_address', tinymce.get('storeAddress')?.getContent() || document.getElementById('storeAddress').value);

        const response = await fetch('../php/admin.php?action=update_store_settings', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            alert('تم تحديث إعدادات المتجر بنجاح');
        } else {
            throw new Error(result.error || 'خطأ غير معروف');
        }
    } catch (error) {
        console.error('Erreur lors de la mise à jour des paramètres:', error);
        alert('Une erreur est survenue lors de la mise à jour des paramètres du magasin');
    }
});

// Load context menu fix
document.addEventListener('DOMContentLoaded', function() {
    // Load the context menu fix script
    const script = document.createElement('script');
    script.src = 'js/context-menu-fix.js';
    script.onload = function() {
        console.log('✅ Context menu fix loaded successfully');

        // Test the fix
        if (window.contextMenuFix && window.contextMenuFix.test) {
            window.contextMenuFix.test();
        }
    };
    script.onerror = function() {
        console.warn('⚠️ Could not load context menu fix, applying inline fix');
        applyInlineContextMenuFix();
    };
    document.head.appendChild(script);
});

// Inline fallback fix
function applyInlineContextMenuFix() {
    // Global error handler for selection-related errors
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message;
            if (message.includes('rangeCount') ||
                message.includes('selection is null') ||
                message.includes('mozInputSource') ||
                message.includes('contextmenuhlpr')) {
                event.preventDefault();
                console.debug('Selection/context menu error prevented:', message);
                return true;
            }
        }
    });

    // Override getSelection with safe wrapper
    if (window.getSelection) {
        const originalGetSelection = window.getSelection;
        window.getSelection = function() {
            try {
                const selection = originalGetSelection.call(window);
                if (!selection) {
                    return {
                        rangeCount: 0,
                        addRange: function() {},
                        removeAllRanges: function() {},
                        toString: function() { return ''; }
                    };
                }
                return selection;
            } catch (error) {
                console.debug('getSelection error handled:', error.message);
                return {
                    rangeCount: 0,
                    addRange: function() {},
                    removeAllRanges: function() {},
                    toString: function() { return ''; }
                };
            }
        };
    }
}

// Add View More link to products table
function addViewMoreLink(row, product) {
    if (product.has_landing_page && product.landing_page_enabled) {
        const actionsCell = row.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = `/product-landing.php?slug=${product.slug}`;
        viewMoreLink.className = 'action-button view-more';
        viewMoreLink.target = '_blank';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i> عرض الصفحة';
        actionsCell.appendChild(viewMoreLink);

        const shareContainer = document.createElement('div');
        shareContainer.className = 'share-buttons';

        // Facebook Share Button
        const fbShareBtn = document.createElement('button');
        fbShareBtn.className = 'share-button facebook';
        fbShareBtn.innerHTML = '<i class="fab fa-facebook-f"></i> فيسبوك';
        fbShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const fbUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(fbUrl, '_blank', 'width=600,height=400');
        };

        // Twitter Share Button
        const twitterShareBtn = document.createElement('button');
        twitterShareBtn.className = 'share-button twitter';
        twitterShareBtn.innerHTML = '<i class="fab fa-twitter"></i> تويتر';
        twitterShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
            window.open(twitterUrl, '_blank', 'width=600,height=400');
        };

        // WhatsApp Share Button
        const whatsappShareBtn = document.createElement('button');
        whatsappShareBtn.className = 'share-button whatsapp';
        whatsappShareBtn.innerHTML = '<i class="fab fa-whatsapp"></i> واتساب';
        whatsappShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(url)}`;
            window.open(whatsappUrl, '_blank');
        };

        // Copy URL Button
        const copyUrlBtn = document.createElement('button');
        copyUrlBtn.className = 'share-button copy';
        copyUrlBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الرابط';
        copyUrlBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            navigator.clipboard.writeText(url).then(() => {
                alert('تم نسخ الرابط بنجاح');

                // Change button text temporarily
                const originalText = copyUrlBtn.innerHTML;
                copyUrlBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalText;
                }, 2000);
            });
        };

        shareContainer.appendChild(fbShareBtn);
        shareContainer.appendChild(twitterShareBtn);
        shareContainer.appendChild(whatsappShareBtn);
        shareContainer.appendChild(copyUrlBtn);
        actionsCell.appendChild(shareContainer);
    }
}

// Navigation dans le panneau d'administration
function initNavigation() {
    console.log('Initializing navigation...');

    // Find navigation items
    const navItems = document.querySelectorAll('.admin-nav ul li');
    console.log('Found navigation items:', navItems.length);

    if (navItems.length === 0) {
        console.error('No navigation items found!');
        return;
    }

    navItems.forEach((item, index) => {
        console.log(`Adding click listener to item ${index}:`, item);

        item.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Navigation item clicked:', this);

            // Handle logout button
            if (this.id === 'logoutBtn') {
                console.log('Logout button clicked');
                if (typeof logout === 'function') {
                    logout();
                }
                return;
            }

            const sectionId = this.getAttribute('data-section');
            if (!sectionId) return;

            console.log('Switching to section:', sectionId);

            // Remove active class from all nav items and sections
            document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                navItem.classList.remove('active');
            });
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Add active class to clicked nav item and corresponding section
            this.classList.add('active');
            const section = document.getElementById(sectionId);

            // Update page title
            updatePageTitle(sectionId);

            if (section) {
                section.classList.add('active');

                // Load section specific content
                switch(sectionId) {
                    case 'dashboard':
                        console.log('Loading dashboard...');
                        if (typeof loadDashboard === 'function') loadDashboard();
                        break;
                    case 'orders':
                        console.log('Loading orders...');
                        if (typeof loadOrders === 'function') loadOrders();
                        break;
                    case 'books':
                        console.log('Loading books...');
                        if (typeof loadProducts === 'function') {
            console.log('📦 Calling loadProducts function...');
            loadProducts();
                        } else {
                            console.error('❌ loadProducts function not found');
                        }
                        break;
                    case 'landingPages':
                        console.log('Loading landing pages...');
                        // Landing pages manager is auto-initialized, just ensure it's ready
                        if (typeof landingPagesManager !== 'undefined') {
                            console.log('Landing Pages Manager is available');
                            // Don't re-initialize, just ensure it's ready
                            if (!landingPagesManager.initialized) {
                                console.log('Landing Pages Manager not initialized yet, initializing...');
                                landingPagesManager.init();
                            } else {
                                console.log('Landing Pages Manager already initialized, refreshing data...');
                                // Always refresh the landing pages data when switching to this section
                                landingPagesManager.loadLandingPages();
                            }
                        } else {
                            console.warn('Landing Pages Manager not found');
                        }
                        break;
                    case 'reports':
                        console.log('Loading reports and analytics...');
                        loadReportsContent();
                        break;
                    case 'settings':
                        console.log('Loading settings...');
                        if (typeof loadSettings === 'function') {
                            loadSettings();
                            // Réinitialiser TinyMCE pour le champ storeAddress
                            if (window.tinymce) {
                                tinymce.remove('#storeAddress');
                                window.initTinyMCE();
                            }
                        }
                        break;
                    case 'aiSettings':
                        console.log('Loading AI settings...');
                        loadAISettingsContent();
                        break;

                    case 'categoriesManagement':
                        console.log('Loading categories management...');
                        loadCategoriesManagementContent();
                        break;
                    case 'paymentSettings':
                        console.log('Loading payment settings...');
                        loadPaymentSettingsContent();
                        break;
                    case 'generalSettings':
                        console.log('Loading general settings...');
                        loadGeneralSettingsContent();
                        break;
                    case 'userManagement':
                        console.log('Loading user management...');
                        loadUserManagementContent();
                        break;
                    case 'securitySettings':
                        console.log('Loading security settings...');
                        loadSecuritySettingsContent();
                        break;
                }

                // Close mobile menu if open
                closeMobileMenu();
            } else {
                console.error('Section not found:', sectionId);
            }
        });
    });

    console.log('Navigation initialization complete');
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');

            // Update toggle icon
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('mobile-open')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const mobileToggle = document.getElementById('mobileMenuToggle');

    if (sidebar && sidebar.classList.contains('mobile-open')) {
        sidebar.classList.remove('mobile-open');

        if (mobileToggle) {
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
    }
}

// Add filters for orders section
function addOrdersFilters() {
    const ordersSection = document.getElementById('orders');
    if (ordersSection && !ordersSection.querySelector('.orders-filters')) {
        const filtersHtml = `
            <div class="orders-filters">
                <div class="filter-group">
                    <label for="statusFilter">الحالة:</label>
                    <select id="statusFilter">
                        <option value="">الكل</option>
                        <option value="en_attente">قيد الانتظار</option>
                        <option value="payé">تم الدفع</option>
                        <option value="expédié">تم الشحن</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">التاريخ:</label>
                    <select id="dateFilter">
                        <option value="">كل التواريخ</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                </div>
            </div>
        `;
        ordersSection.insertAdjacentHTML('afterbegin', filtersHtml);

        // Add event listeners for filters
        const filters = ordersSection.querySelectorAll('select');
        filters.forEach(filter => {
            filter.addEventListener('change', () => {
                const activeFilters = {
                    status: document.getElementById('statusFilter').value,
                    date: document.getElementById('dateFilter').value
                };
                loadOrders(activeFilters);
            });
        });
    }
}

// Load dashboard data
async function loadDashboard() {
    try {
        const response = await fetch('/Mossaab-LandingPage/php/api/dashboard-stats.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'خطأ في تحميل الإحصائيات');
        }

        const data = result.data;
        if (!data || typeof data !== 'object') {
            throw new Error('بيانات غير صالحة');
        }

        console.log('Dashboard data loaded:', data);

        // Update dashboard stats with real data
        const totalBooksElement = document.getElementById('totalBooks');
        const newOrdersElement = document.getElementById('newOrders');
        const totalSalesElement = document.getElementById('totalSales');
        const totalLandingPagesElement = document.getElementById('totalLandingPages');

        // Safely access nested properties with optional chaining and nullish coalescing
        if (totalBooksElement) {
            totalBooksElement.textContent = data?.products?.total ?? 0;
        }
        if (newOrdersElement) {
            newOrdersElement.textContent = data?.orders?.pending ?? 0;
        }
        if (totalSalesElement) {
            totalSalesElement.textContent = data?.sales?.formatted_total ?? '0.00 دج';
        }
        if (totalLandingPagesElement) {
            totalLandingPagesElement.textContent = data?.landing_pages?.total ?? 0;
        }

        // Update recent orders table if it exists
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody && data.recent_orders) {
            recentOrdersBody.innerHTML = '';

            data.recent_orders.forEach(order => {
                const tr = document.createElement('tr');
                tr.style.cursor = 'pointer';
                tr.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.nom_client || 'غير محدد'}</td>
                    <td>${order.montant_total || 0} دج</td>
                    <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                    <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                `;
                tr.onclick = () => {
                    document.querySelector('[data-section="orders"]').click();
                    setTimeout(() => showOrderDetails(order.id), 100);
                };
                recentOrdersBody.appendChild(tr);
            });
        }

        console.log('✅ Dashboard statistics updated successfully');
    } catch (error) {
        console.error('❌ Error loading dashboard stats:', error);
        // Set default values instead of showing error notification
        const totalBooksEl = document.getElementById('totalBooks');
        const newOrdersEl = document.getElementById('newOrders');
        const totalSalesEl = document.getElementById('totalSales');
        const totalLandingPagesEl = document.getElementById('totalLandingPages');

        if (totalBooksEl) totalBooksEl.textContent = '0';
        if (newOrdersEl) newOrdersEl.textContent = '0';
        if (totalSalesEl) totalSalesEl.textContent = '0 دج';
        if (totalLandingPagesEl) totalLandingPagesEl.textContent = '0';

        // Clear recent orders table
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody) {
            recentOrdersBody.innerHTML = '<tr><td colspan="5">لا توجد بيانات متاحة</td></tr>';
        }
    }
}

// Charger la liste des produits
async function loadProducts() {
    try {
        console.log('📦 Loading products...');
        const response = await fetch('../php/api/products.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        console.log('API Response:', result);

        // Handle both array and object responses (updated for new API format)
        let data = [];
        if (Array.isArray(result)) {
            data = result;
        } else if (result.success && Array.isArray(result.data)) {
            data = result.data;
        } else if (result.success && Array.isArray(result.products)) {
            // Backward compatibility
            data = result.products;
        } else if (result.data) {
            data = result.data;
        } else if (result.products) {
            // Backward compatibility
            data = result.products;
        } else {
            console.error('Invalid API response format:', result);
            console.error('Expected: {success: true, data: [...]} or array');
            throw new Error('Invalid data format: no products found in response');
        }

        // Log data for debugging
        console.log('Products data:', data);
        console.log(`Found ${data.length} products`);

        const tbody = document.querySelector('#booksTable tbody');
        tbody.innerHTML = '';

        data.forEach(product => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>
                        <input type="checkbox" class="product-checkbox" value="${product.id}" onchange="updateSelectedProductsCount()">
                    </td>
                    <td><img src="${product.image_url || '../images/default-product.jpg'}" alt="${product.titre}" width="50" onerror="this.src='../images/default-product.jpg'"></td>
                    <td>${product.titre}</td>
                    <td>${getProductTypeText(product.type)}</td>
                    <td>${product.prix} دج</td>
                    <td>${product.stock}</td>
                    <td>
                        <span class="status-badge status-${product.actif ? 'active' : 'inactive'}">
                            ${product.actif ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button onclick="editProduct(${product.id})" class="action-button" title="تعديل المنتج">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleProductStatus(${product.id}, ${product.actif})"
                                class="action-button toggle-status-btn"
                                data-product-id="${product.id}"
                                data-current-status="${product.actif}"
                                style="background: ${product.actif ? '#e67e22' : '#27ae60'};"
                                title="${product.actif ? 'تعطيل المنتج' : 'تفعيل المنتج'}">
                            <i class="fas ${product.actif ? 'fa-pause' : 'fa-play'}"></i>
                            <span class="status-text">${product.actif ? 'مفعل' : 'معطل'}</span>
                        </button>
                        <button onclick="deleteProduct(${product.id})" class="action-button" style="background: #e74c3c;" title="حذف المنتج">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);

                // Add View More link if product has landing page
                addViewMoreLink(tr, product);
            });
        } catch (error) {
            console.error('Error loading books:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
}

// Get product type text in Arabic
function getProductTypeText(type) {
    const types = {
        'book': 'كتاب',
        'backpack': 'حقيبة ظهر',
        'laptop': 'حاسوب محمول'
    };
    return types[type] || type;
}

// Enhanced function to toggle product status
async function toggleProductStatus(productId, currentStatus) {
    console.log('Toggle product status called:', { productId, currentStatus });

    const button = document.querySelector(`[data-product-id="${productId}"]`);
    const originalButtonContent = button ? button.innerHTML : '';

    // Show loading state
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    }

    try {
        const formData = new FormData();
        formData.append('productId', productId.toString());
        formData.append('active', !currentStatus ? '1' : '0');

        console.log('Sending request with data:', {
            productId: productId.toString(),
            active: !currentStatus ? '1' : '0'
        });

        const response = await fetch('../php/api/products.php?action=toggle-active', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        // Get response text first to handle both JSON and non-JSON responses
        const responseText = await response.text();
        console.log('Response text:', responseText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}, response: ${responseText}`);
        }

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error(`Invalid JSON response: ${responseText}`);
        }

        console.log('Parsed response data:', data);

        if (data.success) {
            // Update button immediately for better UX
            const newStatus = !currentStatus;
            if (button) {
                button.style.background = newStatus ? '#e67e22' : '#27ae60';
                button.title = newStatus ? 'تعطيل المنتج' : 'تفعيل المنتج';
                button.innerHTML = `<i class="fas ${newStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${newStatus ? 'مفعل' : 'معطل'}</span>`;
                button.setAttribute('data-current-status', newStatus);
                button.onclick = () => toggleProductStatus(productId, newStatus);
                button.disabled = false;
            }

            // Show success notification
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess(`تم ${newStatus ? 'تفعيل' : 'تعطيل'} المنتج بنجاح`);
            }

            // Refresh the product selection in landing page modal if it's open
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.refreshProductSelect) {
                landingPagesManager.refreshProductSelect();
            }

        } else {
            throw new Error(data.message || data.error || 'فشل في تحديث حالة المنتج');
        }

    } catch (error) {
        console.error('Error toggling product status:', error);

        // Reset button state
        if (button) {
            button.disabled = false;
            button.innerHTML = originalButtonContent;
            button.style.background = currentStatus ? '#e67e22' : '#27ae60';
            button.innerHTML = `<i class="fas ${currentStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${currentStatus ? 'مفعل' : 'معطل'}</span>`;
        }

        notificationManager.showError(error.message || 'حدث خطأ أثناء تحديث حالة المنتج');
    }
}

// Charger la liste des commandes
async function loadOrders(filters = {}) {
    try {
        let url = '../php/orders.php';
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams(filters);
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        if (!text.trim()) {
            console.warn('Empty response from orders API');
            return;
        }

        const orders = JSON.parse(text);
        const ordersTableBody = document.querySelector('#ordersTable tbody');
        ordersTableBody.innerHTML = '';

        orders.forEach(order => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>#${order.id}</td>
                <td>
                    <div class="customer-info">
                        <strong>${order.nom_client}</strong>
                        <span class="customer-email">${order.email || ''}</span>
                    </div>
                </td>
                <td>
                    <div class="order-details">
                        <strong>${order.montant_total} دج</strong>
                        <span class="items-count">${order.items ? order.items.length : 0} منتجات</span>
                    </div>
                </td>
                <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                <td>
                    <button onclick="showOrderDetails(${order.id})" class="action-button"><i class="fas fa-eye"></i></button>
                    <button onclick="printOrder(${order.id})" class="action-button"><i class="fas fa-print"></i></button>
                    <select onchange="updateOrderStatus(${order.id}, this.value)">
                        <option value="en_attente" ${order.statut === 'en_attente' ? 'selected' : ''}>قيد الانتظار</option>
                        <option value="payé" ${order.statut === 'payé' ? 'selected' : ''}>تم الدفع</option>
                        <option value="expédié" ${order.statut === 'expédié' ? 'selected' : ''}>تم الشحن</option>
                    </select>
                </td>
            `;
            ordersTableBody.appendChild(tr);
        });
    } catch (error) {
        console.error('Error loading orders:', error);
    }
}

// Initialize TinyMCE
function initTinyMCE() {
    console.log('Starting TinyMCE initialization...');

    // Check if TinyMCE is loaded
    if (typeof tinymce === 'undefined') {
        console.warn('TinyMCE not loaded yet, waiting...');
        setTimeout(initTinyMCE, 500);
        return;
    }

    // Add tinymce class to target textareas
    const textareas = document.querySelectorAll('#productDescription, .block-content');
    if (textareas.length === 0) {
        console.warn('No textareas found, waiting for DOM...');
        setTimeout(initTinyMCE, 500);
        return;
    }

    textareas.forEach(el => {
        el.classList.add('tinymce');
        console.log('Added tinymce class to:', el.id || 'unnamed textarea');
    });
    console.log('TinyMCE initialization - Found textareas:', textareas.length);

    // Use configuration from tinymce-config.js
    if (typeof tinymce !== 'undefined' && typeof tinymce.init === 'function') {
        tinymce.init({
            selector: 'textarea.tinymce',
            language: 'ar',
            language_url: '/admin/js/langs/ar.js',
            directionality: 'rtl',
            content_css: [
                'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap',
                '/admin/css/tinymce-content.css'
            ],
            font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
                'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | preview fullscreen',
            promotion: false,
            branding: false,
            min_height: 400,
            resize: true,
            setup: function(editor) {
                editor.on('init', function() {
                    console.log('TinyMCE editor initialized:', editor.id);
                    editor.getContainer().style.direction = 'rtl';
                });

                editor.on('change', function() {
                    console.log('Content changed in editor:', editor.id);
                    console.log('Current content:', editor.getContent());
                });
            }
        });
    }
}

// Global error handler for selection issues
window.addEventListener('error', function(e) {
    if (e.message && (e.message.includes('rangeCount') || e.message.includes('selection is null') || e.message.includes('can\'t access property "rangeCount"'))) {
        console.debug('DOM selection error handled globally:', e.message);
        e.preventDefault();
        return true;
    }
});



// Initialize form handlers
function initFormHandlers() {
    console.log('Initializing form handlers...');

    // Check if the form exists
    let productForm = document.getElementById('productForm');
    if (!productForm) {
        console.log('Product form not found in DOM yet...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    // Wait for TinyMCE to be fully loaded
    const productDescriptionTextarea = document.getElementById('productDescription');
    if (!productDescriptionTextarea) {
        console.log('Product description textarea not found...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    if (typeof tinymce === 'undefined' || !tinymce.get('productDescription')) {
        console.log('Waiting for TinyMCE to be ready...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    console.log('All requirements met, proceeding with form initialization...');

    // Handle product type selection
    const productTypeSelect = document.getElementById('productType');
    if (productTypeSelect) {
        productTypeSelect.addEventListener('change', (e) => {
            const productType = e.target.value;
            document.querySelectorAll('.field-group').forEach(group => {
                group.style.display = 'none';
                group.classList.remove('active');
            });
            const targetField = document.getElementById(`${productType}Fields`);
            if (targetField) {
                targetField.style.display = 'block';
                targetField.classList.add('active');
            }
        });
    }

    // Handle product form submission
    productForm = document.getElementById('productForm');
    if (productForm) {
        productForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const productType = document.getElementById('productType').value;

            // Common fields
            formData.append('productType', productType);
            formData.append('productTitle', document.getElementById('productTitle').value);

            // Get description content safely
            let description = '';
            const editor = tinymce.get('productDescription');
            if (editor) {
                if (!editor.initialized) {
                    console.warn('TinyMCE editor not fully initialized yet');
                    alert('يرجى الانتظار حتى يتم تحميل محرر النص بالكامل');
                    return;
                }
                console.log('TinyMCE editor found and initialized');
                description = editor.getContent();
                console.log('Description content:', description);
                if (!description.trim()) {
                    console.warn('Empty description content');
                    alert('يرجى إدخال وصف للمنتج');
                    editor.focus();
                    return;
                }
            } else {
                console.warn('TinyMCE editor not found, falling back to textarea value');
                const textarea = document.getElementById('productDescription');
                description = textarea ? textarea.value.trim() : '';
                if (!description) {
                    alert('يرجى إدخال وصف للمنتج');
                    textarea.focus();
                    return;
                }
            }
            formData.append('productDescription', description);

            formData.append('productPrice', document.getElementById('productPrice').value);
            formData.append('productStock', document.getElementById('productStock').value);

            // Category field
            const categoryField = document.getElementById('productCategory');
            if (categoryField && categoryField.value) {
                formData.append('category_id', categoryField.value);
            }

            // Type-specific fields
            switch(productType) {
                case 'book':
                    formData.append('auteur', document.getElementById('productAuthor').value);
                    break;
                case 'backpack':
                    formData.append('material', document.getElementById('backpackMaterial').value);
                    formData.append('capacity', document.getElementById('backpackCapacity').value);
                    break;
                case 'laptop':
                    formData.append('processor', document.getElementById('laptopProcessor').value);
                    formData.append('ram', document.getElementById('laptopRam').value);
                    formData.append('storage', document.getElementById('laptopStorage').value);
                    break;
            }

            const imageFile = document.getElementById('productImage').files[0];
            if (imageFile) {
                formData.append('productImage', imageFile);
            }

            // Landing page data (if productLandingManager exists)
            if (typeof productLandingManager !== 'undefined') {
                const landingData = productLandingManager.getFormData();
                formData.append('has_landing_page', landingData.hasLandingPage);
                formData.append('landing_page_enabled', landingData.landingPageEnabled);

                // Append content blocks
                landingData.contentBlocks.forEach((block, index) => {
                    formData.append(`content_blocks[${index}][title]`, block.title);
                    formData.append(`content_blocks[${index}][content]`, block.content);
                    formData.append(`content_blocks[${index}][sort_order]`, block.sortOrder);
                });

                // Append gallery images
                landingData.galleryImages.forEach((file, index) => {
                    formData.append(`gallery_images[${index}]`, file);
                });
            }

            const bookId = e.target.getAttribute('data-book-id');
            const method = 'POST';
            const url = '../php/api/products.php';
            if (bookId) {
                formData.append('productId', bookId);
            }

            // Log FormData contents
            console.log('FormData contents:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                console.log('Raw server response:', text);
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        closeModal('productModal');
                        loadProducts();
                    } else {
                        alert(data.error || 'حدث خطأ أثناء حفظ المنتج');
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.log('Response that failed to parse:', text);
                    throw new Error('Invalid JSON response from server');
                }
            })
            .catch(error => {
                console.error('Error saving product:', error);
                alert('حدث خطأ أثناء حفظ المنتج');
            });
        });
    }
}

// Fonctions utilitaires
function getStatusText(status) {
    const statusMap = {
        'en_attente': 'قيد الانتظار',
        'payé': 'تم الدفع',
        'expédié': 'تم الشحن'
    };
    return statusMap[status] || status;
}

// Load categories for product form
async function loadProductCategories() {
    try {
        console.log('Loading categories for product form...');
        const response = await fetch('/php/api/categories.php?active_only=1');
        const data = await response.json();

        if (data.success && data.categories) {
            const categorySelect = document.getElementById('productCategory');
            if (categorySelect) {
                // Clear existing options except the first one
                categorySelect.innerHTML = '<option value="">اختر الفئة...</option>';

                // Add categories as options
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.nom_ar;
                    categorySelect.appendChild(option);
                });

                console.log('✅ Categories loaded successfully:', data.categories.length);
            }
        } else {
            console.error('Failed to load categories:', data.message);
            notificationManager.showError('فشل في تحميل الفئات');
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        notificationManager.showError('خطأ في تحميل الفئات');
    }
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function logout() {
    fetch('../php/admin.php?action=logout', {
        method: 'POST'
    })
    .then(() => {
        window.location.href = 'login.html';
    });
}

// Initialize modal handlers
function initModalHandlers() {
    // Gestionnaires d'événements pour les modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            productForm = document.getElementById('productForm');
            const modalTitle = document.getElementById('modalTitle');
            if (productForm) productForm.reset();
            if (productForm) productForm.removeAttribute('data-product-id');
            if (modalTitle) modalTitle.textContent = 'إضافة منتج جديد';
            loadProductCategories(); // Load categories when opening the form
            showModal('productModal');
        });
    }

    // Bulk deletion event listeners
    const selectAllProductsBtn = document.getElementById('selectAllProducts');
    if (selectAllProductsBtn) {
        selectAllProductsBtn.addEventListener('change', toggleSelectAllProducts);
    }

    const deleteSelectedProductsBtn = document.getElementById('deleteSelectedProductsBtn');
    if (deleteSelectedProductsBtn) {
        deleteSelectedProductsBtn.addEventListener('click', deleteSelectedProducts);
    }

    // Landing pages bulk deletion event listeners
    const selectAllLandingPagesBtn = document.getElementById('selectAllLandingPages');
    if (selectAllLandingPagesBtn) {
        selectAllLandingPagesBtn.addEventListener('change', function() {
            if (typeof window.toggleSelectAllLandingPages === 'function') {
                window.toggleSelectAllLandingPages();
            }
        });
    }

    const deleteSelectedLandingPagesBtn = document.getElementById('deleteSelectedLandingPagesBtn');
    if (deleteSelectedLandingPagesBtn) {
        deleteSelectedLandingPagesBtn.addEventListener('click', function() {
            if (typeof window.deleteSelectedLandingPages === 'function') {
                window.deleteSelectedLandingPages();
            }
        });
    }
}

// Initialize settings form handlers
function initSettingsHandlers() {
    console.log('Initializing settings handlers...');

    // Handle settings section navigation
    const settingItems = document.querySelectorAll('.setting-item[data-section]');
    console.log('Found setting items:', settingItems.length);

    settingItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            console.log('Settings section clicked:', sectionId);

            if (sectionId) {
                // Remove active class from all nav items and sections
                document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Add active class to settings nav item and corresponding section
                const settingsNavItem = document.querySelector('.admin-nav ul li[data-section="settings"]');
                if (settingsNavItem) {
                    settingsNavItem.classList.add('active');
                }

                const section = document.getElementById(sectionId);
                if (section) {
                    section.classList.add('active');

                    // Load section specific content
                    switch(sectionId) {
                        case 'categoriesManagement':
                            console.log('Loading categories management...');
                            loadCategoriesManagementContent();
                            break;
                        case 'paymentSettings':
                            console.log('Loading payment settings...');
                            loadPaymentSettingsContent();
                            break;
                        case 'generalSettings':
                            console.log('Loading general settings...');
                            loadGeneralSettingsContent();
                            break;
                        case 'userManagement':
                            console.log('Loading user management...');
                            loadUserManagementContent();
                            break;
                        case 'securitySettings':
                            console.log('Loading security settings...');
                            loadSecuritySettingsContent();
                            break;
                    }
                } else {
                    console.error('Section not found:', sectionId);
                }
            }
        });
    });



    // Handle store settings form
    const storeSettingsForm = document.getElementById('storeSettingsForm');
    if (storeSettingsForm) {
        storeSettingsForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = {
                store_name: document.getElementById('storeName').value,
                store_phone: document.getElementById('storePhone').value,
                store_email: document.getElementById('storeEmail').value,
                store_address: document.getElementById('storeAddress').value
            };

            try {
                const response = await fetch('../php/admin.php?action=update_store_settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }

                const data = JSON.parse(text);
                if (data.success) {
                    alert('تم حفظ إعدادات المتجر بنجاح');
                } else {
                    alert(data.error || 'حدث خطأ أثناء حفظ إعدادات المتجر');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ إعدادات المتجر');
            }
        });
    }
}

// Add View More link for products with landing pages
function addViewMoreLink(tr, product) {
    console.log('Adding view more link for product:', {
        id: product.id,
        has_landing_page: product.has_landing_page,
        landing_url: product.landing_url
    });

    if (product.has_landing_page) {
        const td = tr.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = product.landing_url;
        viewMoreLink.target = '_blank';
        viewMoreLink.className = 'view-more-link';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i>';
        td.appendChild(viewMoreLink);
    }
}

// Load store settings on page load
async function loadStoreSettings() {
    try {
        const response = await fetch('../php/api/store-settings.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        if (!text.trim()) {
            console.warn('Empty response from store settings API');
            return;
        }

        let data;
        try {
            data = JSON.parse(text);
        } catch (parseError) {
            console.warn('Invalid JSON response from store settings API:', text.substring(0, 100));
            return;
        }

        if (!data || typeof data !== 'object') {
            throw new Error('Invalid response format');
        }

        if (data.success && data.data) {
            console.log('Store settings loaded successfully:', data.data);
        }

        if (data.success && data.settings) {
            // Map API field names to form field names
            const storeNameEl = document.getElementById('storeName');
            const storePhoneEl = document.getElementById('storePhone');
            const storeEmailEl = document.getElementById('storeEmail');
            const storeAddressEl = document.getElementById('storeAddress');

            if (storeNameEl) storeNameEl.value = data.settings.store_name || '';
            if (storePhoneEl) storePhoneEl.value = data.settings.phone_number || '';
            if (storeEmailEl) storeEmailEl.value = data.settings.contact_email || '';

            // Handle address field (might be TinyMCE)
            if (window.tinymce) {
                const editor = tinymce.get('storeAddress');
                if (editor) {
                    editor.setContent(data.settings.address || '');
                    // Use the new API for setting editor mode
                    if (editor.mode && typeof editor.mode.set === 'function') {
                        editor.mode.set('design');
                    }
                }
            } else if (storeAddressEl) {
                storeAddressEl.value = data.settings.address || '';
            }

            console.log('Store settings loaded successfully');
        } else {
            console.warn('Store settings response format:', data);
            throw new Error(data.error || 'Failed to load store settings');
        }
    } catch (error) {
        console.error('Error loading store settings:', error);
        // Set default values instead of showing error notification
        const storeNameEl = document.getElementById('storeName');
        const storePhoneEl = document.getElementById('storePhone');
        const storeEmailEl = document.getElementById('storeEmail');
        const storeAddressEl = document.getElementById('storeAddress');

        if (storeNameEl) storeNameEl.value = '';
        if (storePhoneEl) storePhoneEl.value = '';
        if (storeEmailEl) storeEmailEl.value = '';
        if (storeAddressEl) storeAddressEl.value = '';
    }
}



// Missing function implementations
async function editProduct(productId) {
    console.log('🖊️ Starting edit for product ID:', productId);

    try {
        // Show loading notification
        notificationManager.showInfo('جاري تحميل بيانات المنتج...');

        const httpResponse = await fetch(`../php/api/products.php?id=${productId}`);
        console.log('📡 Response status:', httpResponse.status);

        if (!httpResponse.ok) {
            throw new Error(`HTTP error! status: ${httpResponse.status}`);
        }

        const text = await httpResponse.text();
        console.log('📄 Raw response:', text.substring(0, 200) + '...');

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let response;
        try {
            response = JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        if (!response || typeof response !== 'object') {
            throw new Error('Invalid response format');
        }

        if (!response.success) {
            throw new Error(response.message || 'Failed to load product data');
        }

        const product = response.data;
        console.log('✅ Product data loaded:', product);

        // Clear form first
        resetProductForm();

        // Populate form with product data
        const titleField = document.getElementById('productTitle');
        const descField = document.getElementById('productDescription');
        const priceField = document.getElementById('productPrice');
        const stockField = document.getElementById('productStock');
        const typeField = document.getElementById('productType');

        if (titleField) titleField.value = product.titre || '';
        if (descField) descField.value = product.description || '';
        if (priceField) priceField.value = product.prix || '';
        if (stockField) stockField.value = product.stock || '';
        if (typeField) typeField.value = product.type || 'book';

        console.log('📝 Basic fields populated');

        // Handle author field for books
        const authorField = document.getElementById('productAuthor');
        if (authorField) {
            authorField.value = product.type === 'book' ? (product.auteur || '') : '';
            const authorGroup = authorField.closest('.form-group');
            if (authorGroup) {
                authorGroup.style.display = product.type === 'book' ? 'block' : 'none';
            }
        }

        // Handle product type specific fields
        handleProductTypeChange(product.type);

        // Handle existing image
        if (product.image_url) {
            displayExistingProductImage(product.image_url);
        }

        // Set form to edit mode
        const form = document.getElementById('productForm');
        if (form) {
            form.setAttribute('data-product-id', productId);
        }

        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = 'تعديل المنتج';
        }

        // Load categories and set the product's category
        await loadProductCategories();
        const categoryField = document.getElementById('productCategory');
        if (categoryField && product.category_id) {
            categoryField.value = product.category_id;
        }

        console.log('✅ Form populated successfully');
        notificationManager.showSuccess('تم تحميل بيانات المنتج بنجاح');

        showModal('productModal');

        // Initialize AI Magic Wand for description field if available
        if (typeof window.initializeAIMagicWand === 'function') {
            setTimeout(() => {
                window.initializeAIMagicWand();
                console.log('🪄 AI Magic Wand initialized for product description');
            }, 100);
        }

    } catch (error) {
        console.error('❌ Error loading product:', error);
        notificationManager.showError('حدث خطأ أثناء تحميل بيانات المنتج: ' + error.message);
    }
}

// Helper function to reset the product form
function resetProductForm() {
    const form = document.getElementById('productForm');
    if (form) {
        form.reset();
        form.removeAttribute('data-product-id');
    }

    // Clear image preview
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.innerHTML = '';
    }

    // Reset modal title
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = 'إضافة منتج جديد';
    }
}

// Helper function to display existing product image
function displayExistingProductImage(imageUrl) {
    const imagePreview = document.getElementById('imagePreview');
    if (!imagePreview || !imageUrl) return;

    imagePreview.innerHTML = `
        <div class="preview-image existing-image" style="background-image: url(${imageUrl})">
            <button type="button" class="remove-image" onclick="removeExistingImage(this)">
                <i class="fas fa-times"></i>
            </button>
            <input type="hidden" name="existing_image" value="${imageUrl}">
        </div>
    `;
}

// Helper function to remove existing image
function removeExistingImage(button) {
    const preview = button.closest('.preview-image');
    if (preview) {
        preview.remove();
    }
}

// Helper function to handle product type changes
function handleProductTypeChange(productType) {
    // Hide all type-specific fields first
    document.querySelectorAll('.field-group').forEach(group => {
        group.style.display = 'none';
        group.classList.remove('active');
    });

    // Show relevant fields for the selected type
    const targetField = document.getElementById(`${productType}Fields`);
    if (targetField) {
        targetField.style.display = 'block';
        targetField.classList.add('active');
    }
}

async function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        try {
            const response = await fetch(`../php/api/products.php?id=${productId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                notificationManager.showSuccess('تم حذف المنتج بنجاح');
                loadProducts();
            } else {
                throw new Error(result.error || 'Failed to delete product');
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            notificationManager.showError('حدث خطأ أثناء حذف المنتج');
        }
    }
}

// Bulk product deletion functions
function updateSelectedProductsCount() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    const count = checkboxes.length;
    const countElement = document.getElementById('selectedProductsCount');
    const deleteButton = document.getElementById('deleteSelectedProductsBtn');

    if (countElement) {
        countElement.textContent = count;
    }

    if (deleteButton) {
        deleteButton.style.display = count > 0 ? 'inline-block' : 'none';
    }

    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    const allCheckboxes = document.querySelectorAll('.product-checkbox');

    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }
}

function toggleSelectAllProducts() {
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');

    productCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedProductsCount();
}

async function deleteSelectedProducts() {
    const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        notificationManager.showError('يرجى تحديد منتج واحد على الأقل للحذف');
        return;
    }

    const confirmMessage = `هل أنت متأكد من حذف ${selectedIds.length} منتج؟`;
    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        // Show loading state
        const deleteButton = document.getElementById('deleteSelectedProductsBtn');
        const originalText = deleteButton.innerHTML;
        deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
        deleteButton.disabled = true;

        const response = await fetch('../php/api/products.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'bulk_delete',
                ids: selectedIds
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            notificationManager.showSuccess(`تم حذف ${selectedIds.length} منتج بنجاح`);
            loadProducts(); // Reload the products list

            // Update dashboard if we're on dashboard
            if (document.getElementById('dashboard').classList.contains('active')) {
                loadDashboard();
            }
        } else {
            throw new Error(result.message || 'فشل في حذف المنتجات');
        }

    } catch (error) {
        console.error('Error deleting products:', error);
        notificationManager.showError('حدث خطأ أثناء حذف المنتجات: ' + error.message);
    } finally {
        // Reset button state
        const deleteButton = document.getElementById('deleteSelectedProductsBtn');
        if (deleteButton) {
            deleteButton.innerHTML = '<i class="fas fa-trash"></i> حذف المحدد (<span id="selectedProductsCount">0</span>)';
            deleteButton.disabled = false;
        }
    }
}

function showOrderDetails(orderId) {
    fetch(`../php/orders.php?id=${orderId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            if (!text.trim()) {
                throw new Error('Empty response from server');
            }
            const order = JSON.parse(text);
            if (order) {
                const orderDetails = document.getElementById('orderDetails');
                orderDetails.innerHTML = `
                    <h4>تفاصيل الطلب #${order.id}</h4>
                    <p><strong>العميل:</strong> ${order.nom_client}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${order.email || 'غير محدد'}</p>
                    <p><strong>الهاتف:</strong> ${order.telephone || 'غير محدد'}</p>
                    <p><strong>العنوان:</strong> ${order.adresse || 'غير محدد'}</p>
                    <p><strong>المبلغ الإجمالي:</strong> ${order.montant_total} دج</p>
                    <p><strong>الحالة:</strong> ${getStatusText(order.statut)}</p>
                    <p><strong>تاريخ الطلب:</strong> ${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</p>
                `;
                showModal('orderModal');
            }
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
        });
}

function printOrder(orderId) {
    window.open(`../php/print-order.php?id=${orderId}`, '_blank');
}

function updateOrderStatus(orderId, newStatus) {
    fetch(`../php/orders.php?id=${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        if (!text.trim()) {
            throw new Error('Empty response from server');
        }
        const result = JSON.parse(text);
        if (result.success) {
            loadOrders();
            alert('تم تحديث حالة الطلب بنجاح');
        } else {
            alert(result.error || 'حدث خطأ أثناء تحديث حالة الطلب');
        }
    })
    .catch(error => {
        console.error('Error updating order status:', error);
        alert('حدث خطأ أثناء تحديث حالة الطلب');
    });
}

// Page refresh functionality
function refreshCurrentPage() {
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (!refreshBtn) return;

    // Add loading state
    refreshBtn.classList.add('loading');
    refreshBtn.disabled = true;

    // Get current active section
    const activeSection = document.querySelector('.content-section.active');
    const currentSection = activeSection ? activeSection.id : 'dashboard';

    // Update page title
    updatePageTitle(currentSection);

    // Refresh data based on current section
    const refreshPromises = [];

    switch (currentSection) {
        case 'dashboard':
            refreshPromises.push(loadDashboard());
            break;
        case 'books':
            refreshPromises.push(loadProducts());
            break;
        case 'orders':
            refreshPromises.push(loadOrders());
            break;
        case 'landingPages':
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                refreshPromises.push(landingPagesManager.loadLandingPages());
                // Also refresh the product selection dropdown
                if (landingPagesManager.loadActiveProducts) {
                    refreshPromises.push(landingPagesManager.loadActiveProducts());
                }
            }
            break;
        default:
            refreshPromises.push(loadDashboard());
    }

    // Wait for all refresh operations to complete
    Promise.all(refreshPromises)
        .then(() => {
            notificationManager.showSuccess('تم تحديث الصفحة بنجاح');
        })
        .catch((error) => {
            console.error('Error refreshing page:', error);
            notificationManager.showError('حدث خطأ أثناء تحديث الصفحة');
        })
        .finally(() => {
            // Remove loading state
            setTimeout(() => {
                refreshBtn.classList.remove('loading');
                refreshBtn.disabled = false;
            }, 500);
        });
}

// Update page title based on current section
function updatePageTitle(sectionId) {
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'books': 'إدارة المنتجات',
        'orders': 'إدارة الطلبات',
        'landingPages': 'صفحات الهبوط',
        'reports': 'التقارير والإحصائيات',
        'settings': 'الإعدادات'
    };

    if (pageTitle) {
        pageTitle.textContent = titles[sectionId] || 'لوحة التحكم';
    }
}

// Initialisation
// Global error handlers
window.addEventListener('error', function(event) {
    // Handle selection-related errors
    if (event.error && event.error.message && (
        event.error.message.includes('rangeCount') ||
        event.error.message.includes('selection is null') ||
        event.error.message.includes('can\'t access property "rangeCount"')
    )) {
        event.preventDefault();
        console.debug('Selection error prevented:', event.error.message);
        return true;
    }

    // Handle JSON parsing errors
    if (event.error instanceof SyntaxError && event.error.message.includes('JSON')) {
        event.preventDefault();
        console.warn('JSON parsing error prevented:', event.error.message);
        return true;
    }
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Starting admin initialization...');

    // Initialize core admin functionality first
    checkAuth();
    notificationManager.init();
    initNavigation();
    initMobileMenu();
    initFormHandlers();
    initModalHandlers();
    initSettingsHandlers();
    initTinyMCE();
    loadDashboard();
    loadStoreSettings();

    // Landing pages manager initializes itself automatically
    // No need to initialize it here to prevent conflicts

    // Initialize refresh button
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshCurrentPage);
    }

    console.log('Admin initialization complete');
});

// Handle page loading completion
window.addEventListener('load', function() {
    // Mark content as loaded to show page after all resources are loaded
    document.body.classList.add('content-loaded');
    // Hide loading indicator
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
    console.log('Page fully loaded');
});

// Test function for landing page modal
function testLandingPageModal() {
    console.log('🧪 Testing landing page modal...');

    // Check if landingPagesManager exists
    if (typeof landingPagesManager !== 'undefined') {
        console.log('✅ landingPagesManager found');
        console.log('Initialized:', landingPagesManager.initialized);

        if (landingPagesManager.initialized) {
            console.log('🚀 Opening modal via landingPagesManager...');
            landingPagesManager.openModal();
        } else {
            console.log('⚠️ landingPagesManager not initialized, trying to initialize...');
            landingPagesManager.init();
            setTimeout(() => {
                landingPagesManager.openModal();
            }, 500);
        }
    } else {
        console.error('❌ landingPagesManager not found');

        // Try direct modal manipulation
        const modal = document.getElementById('landingPageModal');
        if (modal) {
            console.log('🔧 Trying direct modal manipulation...');
            modal.style.display = 'block';
            modal.style.opacity = '1';
            modal.style.visibility = 'visible';
            modal.style.zIndex = '9999';
        } else {
            console.error('❌ Modal element not found');
        }
    }
}

// Safe wrapper for testLandingPageModal
function safeTestLandingPageModal() {
    console.log('🧪 Safe test landing page modal called...');

    if (typeof landingPagesManager !== 'undefined') {
        // Ensure initialization
        if (!landingPagesManager.initialized) {
            console.log('🔧 Landing pages manager not initialized, initializing now...');
            try {
                landingPagesManager.init();
            } catch (error) {
                console.error('Failed to initialize landing pages manager:', error);
                if (typeof notificationManager !== 'undefined') {
                    notificationManager.showError('فشل في تهيئة نظام إدارة صفحات الهبوط');
                }
                return;
            }
        }

        // Call the test function
        testLandingPageModal();
    } else {
        console.error('landingPagesManager not available');
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('نظام إدارة صفحات الهبوط غير متاح');
        }
    }
}

// Load Reports content
function loadReportsContent() {
    console.log('Loading reports content...');

    // Load and execute the reports JavaScript
    if (!document.querySelector('script[src="js/reports.js"]')) {
        const script = document.createElement('script');
        script.src = 'js/reports.js';
        script.onload = () => {
            console.log('Reports script loaded successfully');
            if (typeof initializeReports === 'function') {
                initializeReports();
            }
        };
        script.onerror = () => {
            console.error('Failed to load Reports script');
        };
        document.head.appendChild(script);
    } else {
        // Script already loaded, just initialize
        if (typeof initializeReports === 'function') {
            initializeReports();
        }
    }

    console.log('Reports content loaded successfully');
}


// Load AI Settings content into the admin panel
async function loadAISettingsContent() {
    console.log('📡 Loading AI settings content...');

    const container = document.getElementById('aiSettingsContent');
    if (!container) {
        console.error('AI settings container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الذكاء الاصطناعي...</p>
            </div>
        `;

        // Fetch the AI settings HTML content
        const response = await fetch('ai-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the AI settings content from the page
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const aiSettingsContent = doc.querySelector('.ai-settings-content') ||
                                 doc.querySelector('.main-content') ||
                                 doc.querySelector('main') ||
                                 doc.querySelector('body');

        if (aiSettingsContent) {
            container.innerHTML = aiSettingsContent.innerHTML;

            // Load AI settings script if not already loaded
            if (!window.aiSettingsLoaded) {
                const script = document.createElement('script');
                script.src = 'js/ai-settings.js';
                script.onload = () => {
                    window.aiSettingsLoaded = true;
                    // Initialize AI settings functionality
                    if (typeof window.loadAISettings === 'function') {
                        window.loadAISettings();
                    }
                    if (typeof window.setupEventListeners === 'function') {
                        window.setupEventListeners();
                    }
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize
                if (typeof window.loadAISettings === 'function') {
                    window.loadAISettings();
                }
                if (typeof window.setupEventListeners === 'function') {
                    window.setupEventListeners();
                }
            }

            console.log('✅ AI settings content loaded successfully');
        } else {
            throw new Error('Could not find AI settings content in page');
        }

    } catch (error) {
        console.error('Error loading AI settings:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                <h3 style="color: #e74c3c; margin: 15px 0;">خطأ في تحميل إعدادات الذكاء الاصطناعي</h3>
                <p style="color: #666;">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button onclick="loadAISettingsContent()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load Categories Management content into the admin panel
async function loadCategoriesManagementContent() {
    console.log('📡 Loading categories management content...');

    const container = document.getElementById('categoriesContent');
    if (!container) {
        console.error('Categories management container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الفئات...</p>
            </div>
        `;

        // Fetch the categories management HTML content
        const response = await fetch('categories-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the categories management content from the page
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const categoriesContent = doc.querySelector('.categories-management-content') ||
                                 doc.querySelector('main') ||
                                 doc.querySelector('.main-content') ||
                                 doc.querySelector('body');

        if (categoriesContent) {
            container.innerHTML = categoriesContent.innerHTML;

            // Load categories management script if not already loaded
            if (!window.categoriesManagementLoaded) {
                const script = document.createElement('script');
                script.src = 'js/categories-management.js';
                script.onload = () => {
                    window.categoriesManagementLoaded = true;
                    // Initialize categories management functionality
                    if (typeof window.initializeCategoriesManagement === 'function') {
                        window.initializeCategoriesManagement();
                    }
                };
                script.onerror = () => {
                    console.log('Categories management script not found, using basic functionality');
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize
                if (typeof window.initializeCategoriesManagement === 'function') {
                    window.initializeCategoriesManagement();
                }
            }

            console.log('✅ Categories management content loaded successfully');
        } else {
            throw new Error('Could not find categories management content in page');
        }

    } catch (error) {
        console.error('Error loading categories management:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                <h3 style="color: #e74c3c; margin: 15px 0;">خطأ في تحميل إدارة الفئات</h3>
                <p style="color: #666;">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button onclick="loadCategoriesManagementContent()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load Payment Settings content into the admin panel
async function loadPaymentSettingsContent() {
    console.log('📡 Loading payment settings content...');

    const container = document.getElementById('paymentSettingsContent');
    if (!container) {
        console.error('Payment settings container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الدفع...</p>
            </div>
        `;

        // Fetch the payment settings HTML content
        const response = await fetch('payment-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the payment settings content from the page
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const paymentContent = doc.querySelector('.payment-settings-content') ||
                              doc.querySelector('main') ||
                              doc.querySelector('.main-content') ||
                              doc.querySelector('body');

        if (paymentContent) {
            container.innerHTML = paymentContent.innerHTML;

            // Load payment settings script if not already loaded
            if (!window.paymentSettingsLoaded) {
                const script = document.createElement('script');
                script.src = 'js/payment-settings.js';
                script.onload = () => {
                    window.paymentSettingsLoaded = true;
                    // Initialize payment settings functionality
                    if (typeof window.initializePaymentSettings === 'function') {
                        window.initializePaymentSettings();
                    }
                };
                script.onerror = () => {
                    console.log('Payment settings script not found, using basic functionality');
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize
                if (typeof window.initializePaymentSettings === 'function') {
                    window.initializePaymentSettings();
                }
            }

            console.log('✅ Payment settings content loaded successfully');
        } else {
            throw new Error('Could not find payment settings content in page');
        }

    } catch (error) {
        console.error('Error loading payment settings:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                <h3 style="color: #e74c3c; margin: 15px 0;">خطأ في تحميل إعدادات الدفع</h3>
                <p style="color: #666;">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button onclick="loadPaymentSettingsContent()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load General Settings content
function loadGeneralSettingsContent() {
    console.log('📡 Loading general settings content...');

    const container = document.getElementById('generalSettingsContent');
    if (!container) {
        console.error('General settings container not found');
        return;
    }

    // Create general settings interface
    container.innerHTML = `
        <div class="settings-header">
            <h1><i class="fas fa-cog"></i> الإعدادات العامة</h1>
            <p>إدارة الإعدادات العامة للنظام</p>
        </div>

        <div class="settings-content">
            <div class="settings-section">
                <h3><i class="fas fa-globe"></i> إعدادات الموقع</h3>
                <div class="form-group">
                    <label>اسم الموقع</label>
                    <input type="text" value="متجر مصعب" class="form-control">
                </div>
                <div class="form-group">
                    <label>وصف الموقع</label>
                    <textarea class="form-control" rows="3">متجر إلكتروني متخصص في بيع الكتب والمنتجات التعليمية</textarea>
                </div>
                <div class="form-group">
                    <label>لغة الموقع الافتراضية</label>
                    <select class="form-control">
                        <option value="ar" selected>العربية</option>
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-clock"></i> إعدادات الوقت</h3>
                <div class="form-group">
                    <label>المنطقة الزمنية</label>
                    <select class="form-control">
                        <option value="Africa/Algiers" selected>الجزائر (GMT+1)</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>تنسيق التاريخ</label>
                    <select class="form-control">
                        <option value="d/m/Y" selected>يوم/شهر/سنة</option>
                        <option value="Y-m-d">سنة-شهر-يوم</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-envelope"></i> إعدادات البريد الإلكتروني</h3>
                <div class="form-group">
                    <label>البريد الإلكتروني للإشعارات</label>
                    <input type="email" value="<EMAIL>" class="form-control">
                </div>
                <div class="form-group">
                    <label>اسم المرسل</label>
                    <input type="text" value="متجر مصعب" class="form-control">
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveGeneralSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button class="btn btn-secondary" onclick="resetGeneralSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;

    console.log('✅ General settings content loaded successfully');
}

// Load User Management content
function loadUserManagementContent() {
    console.log('📡 Loading user management content...');

    const container = document.getElementById('userManagementContent');
    if (!container) {
        console.error('User management container not found');
        return;
    }

    // Create user management interface
    container.innerHTML = `
        <div class="settings-header">
            <h1><i class="fas fa-users"></i> إدارة المستخدمين</h1>
            <p>إدارة حسابات المستخدمين والصلاحيات</p>
        </div>

        <div class="settings-content">
            <div class="user-actions">
                <button class="btn btn-primary" onclick="addNewUser()">
                    <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                </button>
                <button class="btn btn-info" onclick="exportUsers()">
                    <i class="fas fa-download"></i> تصدير قائمة المستخدمين
                </button>
            </div>

            <div class="users-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>مدير النظام</td>
                            <td><EMAIL></td>
                            <td><span class="badge badge-danger">مدير</span></td>
                            <td>2024-01-01</td>
                            <td><span class="badge badge-success">نشط</span></td>
                            <td>
                                <button class="btn btn-sm btn-warning" onclick="editUser(1)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewUser(1)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>موظف المبيعات</td>
                            <td><EMAIL></td>
                            <td><span class="badge badge-primary">موظف</span></td>
                            <td>2024-02-15</td>
                            <td><span class="badge badge-success">نشط</span></td>
                            <td>
                                <button class="btn btn-sm btn-warning" onclick="editUser(2)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewUser(2)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteUser(2)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-shield-alt"></i> إعدادات الصلاحيات</h3>
                <div class="permissions-grid">
                    <div class="permission-item">
                        <label>
                            <input type="checkbox" checked> إدارة المنتجات
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox" checked> إدارة الطلبات
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox"> إدارة المستخدمين
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox"> إعدادات النظام
                        </label>
                    </div>
                </div>
            </div>
        </div>
    `;

    console.log('✅ User management content loaded successfully');
}

// Load Security Settings content
function loadSecuritySettingsContent() {
    console.log('📡 Loading security settings content...');

    const container = document.getElementById('securitySettingsContent');
    if (!container) {
        console.error('Security settings container not found');
        return;
    }

    // Create security settings interface
    container.innerHTML = `
        <div class="settings-header">
            <h1><i class="fas fa-shield-alt"></i> إعدادات الأمان</h1>
            <p>إدارة إعدادات الأمان والحماية</p>
        </div>

        <div class="settings-content">
            <div class="settings-section">
                <h3><i class="fas fa-lock"></i> إعدادات كلمة المرور</h3>
                <div class="form-group">
                    <label>الحد الأدنى لطول كلمة المرور</label>
                    <input type="number" value="8" min="6" max="20" class="form-control">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> يجب أن تحتوي على أحرف كبيرة وصغيرة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> يجب أن تحتوي على أرقام
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> يجب أن تحتوي على رموز خاصة
                    </label>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-user-shield"></i> إعدادات تسجيل الدخول</h3>
                <div class="form-group">
                    <label>عدد محاولات تسجيل الدخول المسموحة</label>
                    <input type="number" value="5" min="3" max="10" class="form-control">
                </div>
                <div class="form-group">
                    <label>مدة الحظر بعد المحاولات الفاشلة (بالدقائق)</label>
                    <input type="number" value="15" min="5" max="60" class="form-control">
                </div>
                <div class="form-group">
                    <label>مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" value="30" min="15" max="120" class="form-control">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تفعيل المصادقة الثنائية
                    </label>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-database"></i> أمان البيانات</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تشفير البيانات الحساسة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تسجيل العمليات الحساسة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> إنشاء نسخ احتياطية تلقائية
                    </label>
                </div>
                <div class="form-group">
                    <label>تكرار النسخ الاحتياطية</label>
                    <select class="form-control">
                        <option value="daily">يومياً</option>
                        <option value="weekly" selected>أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-history"></i> سجل الأنشطة</h3>
                <div class="activity-log">
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:30:25</span>
                        <span class="log-action">تسجيل دخول ناجح</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:25:10</span>
                        <span class="log-action">تعديل إعدادات المنتج</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:20:05</span>
                        <span class="log-action">إضافة منتج جديد</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveSecuritySettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button class="btn btn-warning" onclick="runSecurityScan()">
                    <i class="fas fa-search"></i> فحص أمني شامل
                </button>
                <button class="btn btn-danger" onclick="clearSecurityLogs()">
                    <i class="fas fa-trash"></i> مسح سجل الأنشطة
                </button>
            </div>
        </div>
    `;

    console.log('✅ Security settings content loaded successfully');
}

/**
 * Load General Settings content dynamically
 */
async function loadGeneralSettingsContent() {
    const container = document.getElementById('generalSettingsContent');
    if (!container) return;

    try {
        console.log('Loading General Settings content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل الإعدادات العامة...</p>
            </div>
        `;

        // Load the general settings HTML content
        const response = await fetch('general-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the general settings content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const generalSettingsContent = doc.querySelector('.general-settings-content') ||
                                      doc.querySelector('.main-content') ||
                                      doc.querySelector('body');

        if (generalSettingsContent) {
            container.innerHTML = generalSettingsContent.innerHTML;
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the general settings JavaScript
        if (!document.querySelector('script[src="js/general-settings.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/general-settings.js';
            script.onload = () => {
                console.log('General Settings script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load General Settings script');
            };
            document.head.appendChild(script);
        }

        console.log('General Settings content loaded successfully');

    } catch (error) {
        console.error('Error loading General Settings content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل الإعدادات العامة</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadGeneralSettingsContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load User Management content dynamically
 */
async function loadUserManagementContent() {
    const container = document.getElementById('userManagementContent');
    if (!container) return;

    try {
        console.log('Loading User Management content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة المستخدمين...</p>
            </div>
        `;

        // Load the user management HTML content
        const response = await fetch('user-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the user management content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const userManagementContent = doc.querySelector('.user-management-content') ||
                                     doc.querySelector('.main-content') ||
                                     doc.querySelector('body');

        if (userManagementContent) {
            container.innerHTML = userManagementContent.innerHTML;
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the user management JavaScript
        if (!document.querySelector('script[src="js/user-management.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/user-management.js';
            script.onload = () => {
                console.log('User Management script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load User Management script');
            };
            document.head.appendChild(script);
        }

        console.log('User Management content loaded successfully');

    } catch (error) {
        console.error('Error loading User Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المستخدمين</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadUserManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load Security Settings content dynamically
 */
async function loadSecuritySettingsContent() {
    const container = document.getElementById('securitySettingsContent');
    if (!container) return;

    try {
        console.log('Loading Security Settings content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الأمان...</p>
            </div>
        `;

        // Load the security settings HTML content
        const response = await fetch('security-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the security settings content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const securitySettingsContent = doc.querySelector('.security-settings-content') ||
                                       doc.querySelector('.main-content') ||
                                       doc.querySelector('body');

        if (securitySettingsContent) {
            container.innerHTML = securitySettingsContent.innerHTML;
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the security settings JavaScript
        if (!document.querySelector('script[src="js/security-settings.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/security-settings.js';
            script.onload = () => {
                console.log('Security Settings script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load Security Settings script');
            };
            document.head.appendChild(script);
        }

        console.log('Security Settings content loaded successfully');

    } catch (error) {
        console.error('Error loading Security Settings content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إعدادات الأمان</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadSecuritySettingsContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}
