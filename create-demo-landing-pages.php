<?php

/**
 * Create 5 Demo Landing Pages for Demo User Products
 */

require_once 'php/config.php';

header('Content-Type: text/html');

echo "<h1>Create 5 Demo Landing Pages</h1>";

try {
    $pdo = getPDOConnection();

    // Get the 5 products we identified
    $productIds = [19, 20, 21, 22, 23];

    // First, clear existing test landing pages for these products
    echo "<h2>Clearing existing test landing pages...</h2>";
    foreach ($productIds as $productId) {
        $stmt = $pdo->prepare("DELETE FROM landing_pages WHERE produit_id = ?");
        $stmt->execute([$productId]);
        echo "<p>Cleared existing landing pages for product ID: $productId</p>";
    }

    echo "<h2>Creating Fresh Landing Pages for Products:</h2>";

    foreach ($productIds as $productId) {
        // Get product details
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();

        if (!$product) {
            echo "<p>❌ Product ID $productId not found</p>";
            continue;
        }

        // Check if landing page already exists
        $stmt = $pdo->prepare("SELECT id FROM landing_pages WHERE produit_id = ?");
        $stmt->execute([$productId]);
        $existingLP = $stmt->fetch();

        if ($existingLP) {
            echo "<p>⚠️ Landing page already exists for product: " . htmlspecialchars($product['titre']) . "</p>";
            continue;
        }

        // Generate unique landing page content based on product type
        $landingPageContent = generateLandingPageContent($product);

        // Create landing page (using correct table structure)
        $stmt = $pdo->prepare("
            INSERT INTO landing_pages (titre, produit_id, template_id, contenu_droit, contenu_gauche, lien_url)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $landingPageTitle = 'صفحة هبوط - ' . $product['titre'];
        $templateId = getTemplateForProduct($product['type']);
        $linkUrl = '/landing-page-template.php?id=' . $productId;

        $stmt->execute([
            $landingPageTitle,
            $productId,
            $templateId,
            $landingPageContent['right'],
            $landingPageContent['left'],
            $linkUrl
        ]);

        $landingPageId = $pdo->lastInsertId();

        // Update the product to mark it as having a landing page
        $stmt = $pdo->prepare("UPDATE produits SET has_landing_page = 1, landing_page_enabled = 1 WHERE id = ?");
        $stmt->execute([$productId]);

        echo "<p>✅ Created landing page for: <strong>" . htmlspecialchars($product['titre']) . "</strong></p>";
        echo "<p>   - Landing Page ID: $landingPageId</p>";
        echo "<p>   - Template: $templateId</p>";
        echo "<p>   - URL: <a href='http://localhost:8000$linkUrl' target='_blank'>$linkUrl</a></p>";
        echo "<hr>";
    }

    echo "<h2>Summary</h2>";
    echo "<p>✅ Demo landing pages creation completed!</p>";
    echo "<p>All 5 products now have unique landing pages with Arabic RTL support.</p>";
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p>❌ " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

/**
 * Generate landing page content based on product type and details
 */
function generateLandingPageContent($product)
{
    $type = $product['type'];
    $title = $product['titre'];
    $description = $product['description'];
    $price = number_format($product['prix'], 2);
    $oldPrice = number_format($product['prix'] * 1.25, 2); // 25% higher for "old price"

    switch ($type) {
        case 'laptop':
            return generateLaptopContent($title, $description, $price, $oldPrice);
        case 'bag':
            return generateBagContent($title, $description, $price, $oldPrice);
        case 'book':
            return generateBookContent($title, $description, $price, $oldPrice, $product['auteur']);
        default:
            return generateGenericContent($title, $description, $price, $oldPrice);
    }
}

/**
 * Generate laptop-specific landing page content
 */
function generateLaptopContent($title, $description, $price, $oldPrice)
{
    $rightContent = "
    <div class='hero-section laptop-hero'>
        <h1 class='hero-title'>💻 $title</h1>
        <p class='hero-subtitle'>الحل المثالي للطلاب والمهنيين - أداء استثنائي وموثوقية عالية</p>
        <div class='price-section'>
            <span class='current-price'>$price دج</span>
            <span class='old-price'>$oldPrice دج</span>
            <span class='discount-badge'>وفر " . number_format(($oldPrice - $price), 2) . " دج</span>
        </div>
        <div class='cta-section'>
            <button class='cta-button primary'>🛒 اطلب الآن</button>
            <button class='cta-button secondary'>📞 اتصل بنا</button>
        </div>
        <div class='trust-indicators'>
            <span>✅ ضمان سنتان</span>
            <span>🚚 توصيل مجاني</span>
            <span>💳 دفع آمن</span>
        </div>
    </div>";

    $leftContent = "
    <div class='product-details laptop-details'>
        <h2>🔧 المواصفات التقنية</h2>
        $description

        <div class='features-grid'>
            <div class='feature-item'>
                <h3>⚡ أداء سريع</h3>
                <p>معالج قوي وذاكرة عشوائية كبيرة لتشغيل جميع التطبيقات بسلاسة</p>
            </div>
            <div class='feature-item'>
                <h3>🔋 بطارية طويلة المدى</h3>
                <p>تدوم حتى 8 ساعات من الاستخدام المتواصل</p>
            </div>
            <div class='feature-item'>
                <h3>💾 تخزين سريع</h3>
                <p>قرص SSD للإقلاع السريع وتحميل التطبيقات</p>
            </div>
            <div class='feature-item'>
                <h3>🎨 شاشة عالية الجودة</h3>
                <p>ألوان زاهية ووضوح استثنائي</p>
            </div>
        </div>

        <div class='guarantee-section'>
            <h3>🛡️ ضماننا لك</h3>
            <ul>
                <li>ضمان شامل لمدة سنتين</li>
                <li>دعم فني مجاني</li>
                <li>استبدال فوري في حالة العيوب</li>
                <li>صيانة مجانية للسنة الأولى</li>
            </ul>
        </div>
    </div>";

    return ['right' => $rightContent, 'left' => $leftContent];
}

/**
 * Generate bag-specific landing page content
 */
function generateBagContent($title, $description, $price, $oldPrice)
{
    $rightContent = "
    <div class='hero-section bag-hero'>
        <h1 class='hero-title'>🎒 $title</h1>
        <p class='hero-subtitle'>الرفيق المثالي لرحلاتك اليومية - عملية وأنيقة</p>
        <div class='price-section'>
            <span class='current-price'>$price دج</span>
            <span class='old-price'>$oldPrice دج</span>
            <span class='discount-badge'>خصم خاص!</span>
        </div>
        <div class='cta-section'>
            <button class='cta-button primary'>🛒 اشتري الآن</button>
            <button class='cta-button secondary'>💬 استفسار</button>
        </div>
        <div class='trust-indicators'>
            <span>✅ جودة مضمونة</span>
            <span>🚚 شحن سريع</span>
            <span>🔄 إرجاع مجاني</span>
        </div>
    </div>";

    $leftContent = "
    <div class='product-details bag-details'>
        <h2>🌟 مميزات الحقيبة</h2>
        $description

        <div class='usage-scenarios'>
            <h3>🎯 مثالية لـ:</h3>
            <div class='scenario-grid'>
                <div class='scenario'>📚 الطلاب</div>
                <div class='scenario'>💼 العمل</div>
                <div class='scenario'>🏃‍♂️ الرياضة</div>
                <div class='scenario'>✈️ السفر</div>
            </div>
        </div>

        <div class='quality-assurance'>
            <h3>🏆 ضمان الجودة</h3>
            <ul>
                <li>مواد عالية الجودة ومقاومة للتآكل</li>
                <li>سحابات معدنية قوية</li>
                <li>خياطة محكمة ومتينة</li>
                <li>تصميم مريح للظهر والكتفين</li>
            </ul>
        </div>

        <div class='care-instructions'>
            <h3>🧼 العناية والصيانة</h3>
            <p>سهلة التنظيف - امسحها بقطعة قماش مبللة وستبدو كالجديدة!</p>
        </div>
    </div>";

    return ['right' => $rightContent, 'left' => $leftContent];
}

/**
 * Generate book-specific landing page content
 */
function generateBookContent($title, $description, $price, $oldPrice, $author)
{
    $rightContent = "
    <div class='hero-section book-hero'>
        <h1 class='hero-title'>📚 $title</h1>
        <p class='hero-subtitle'>بقلم: $author</p>
        <p class='book-tagline'>استثمر في نفسك - اقرأ كتاباً يغير حياتك</p>
        <div class='price-section'>
            <span class='current-price'>$price دج</span>
            <span class='old-price'>$oldPrice دج</span>
            <span class='discount-badge'>عرض محدود!</span>
        </div>
        <div class='cta-section'>
            <button class='cta-button primary'>📖 اطلب نسختك</button>
            <button class='cta-button secondary'>👁️ معاينة</button>
        </div>
        <div class='trust-indicators'>
            <span>✅ كتاب أصلي</span>
            <span>📦 تغليف آمن</span>
            <span>⭐ تقييم عالي</span>
        </div>
    </div>";

    $leftContent = "
    <div class='product-details book-details'>
        <h2>📖 عن الكتاب</h2>
        $description

        <div class='book-benefits'>
            <h3>💡 ماذا ستتعلم؟</h3>
            <div class='benefits-list'>
                <div class='benefit'>🎯 استراتيجيات عملية للنجاح</div>
                <div class='benefit'>🧠 تطوير العقلية الإيجابية</div>
                <div class='benefit'>📈 تحسين الأداء الشخصي</div>
                <div class='benefit'>🚀 تحقيق الأهداف بفعالية</div>
            </div>
        </div>

        <div class='author-section'>
            <h3>✍️ عن المؤلف</h3>
            <p><strong>$author</strong> - كاتب ومؤلف معروف في مجال التطوير الذاتي والنجاح.</p>
        </div>

        <div class='reader-testimonials'>
            <h3>💬 آراء القراء</h3>
            <blockquote>\"كتاب رائع غير نظرتي للحياة وساعدني على تحقيق أهدافي\"</blockquote>
            <blockquote>\"أنصح الجميع بقراءة هذا الكتاب - استثمار حقيقي في النفس\"</blockquote>
        </div>
    </div>";

    return ['right' => $rightContent, 'left' => $leftContent];
}

/**
 * Generate generic content for other product types
 */
function generateGenericContent($title, $description, $price, $oldPrice)
{
    $rightContent = "
    <div class='hero-section generic-hero'>
        <h1 class='hero-title'>⭐ $title</h1>
        <p class='hero-subtitle'>منتج عالي الجودة بسعر مميز</p>
        <div class='price-section'>
            <span class='current-price'>$price دج</span>
            <span class='old-price'>$oldPrice دج</span>
        </div>
        <div class='cta-section'>
            <button class='cta-button primary'>🛒 اطلب الآن</button>
        </div>
    </div>";

    $leftContent = "
    <div class='product-details generic-details'>
        <h2>📋 تفاصيل المنتج</h2>
        $description
    </div>";

    return ['right' => $rightContent, 'left' => $leftContent];
}

/**
 * Get appropriate template based on product type
 */
function getTemplateForProduct($type)
{
    switch ($type) {
        case 'laptop':
            return 'tech';
        case 'bag':
            return 'lifestyle';
        case 'book':
            return 'education';
        default:
            return 'modern';
    }
}
