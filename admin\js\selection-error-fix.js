/**
 * Comprehensive Selection Error Fix for Firefox Extension Conflicts
 * Specifically targets contextmenuhlpr.js and similar extension-related errors
 */

(function() {
    'use strict';

    console.log('🛡️ Advanced Selection Error Fix loaded');

    // Global error suppression for known problematic patterns
    const problematicPatterns = [
        /can't access property.*rangeCount.*selection is null/i,
        /contextmenuhlpr/i,
        /mozInputSource/i,
        /getSelection.*null/i,
        /selection.*rangeCount/i
    ];

    // Enhanced error patterns including subscription management errors
    const subscriptionErrorPatterns = [
        /can't access property.*value.*null/i,
        /getElementById.*null/i,
        /subscriptionId.*null/i,
        /subscriptionName.*null/i
    ];

    // Override console.error to filter out known extension errors
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        if (problematicPatterns.some(pattern => pattern.test(message)) ||
            subscriptionErrorPatterns.some(pattern => pattern.test(message))) {
            console.log('🛡️ Filtered extension/DOM error:', message);
            return;
        }
        originalConsoleError.apply(console, args);
    };

    // Enhanced global error handler
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message;
            if (problematicPatterns.some(pattern => pattern.test(message))) {
                console.log('🛡️ Suppressed extension error:', message);
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        }
        return false;
    }, true);

    // Override the problematic contextmenuhlpr.js behavior
    window.addEventListener('DOMContentLoaded', function() {
        // Immediate protection
        protectSelectionAPI();

        // Wait for extensions to load
        setTimeout(function() {
            protectSelectionAPI();
            overrideEventListeners();
        }, 100);

        // Additional protection after TinyMCE loads
        setTimeout(function() {
            protectSelectionAPI();
            protectTinyMCE();
        }, 1000);
    });

    function protectSelectionAPI() {
        // Override getSelection to always return a safe object
        if (window.getSelection) {
            const originalGetSelection = window.getSelection;

            window.getSelection = function() {
                try {
                    const selection = originalGetSelection.call(window);
                    if (!selection) {
                        return createSafeSelection();
                    }
                    return wrapSelection(selection);
                } catch (error) {
                    console.log('🛡️ getSelection error handled:', error.message);
                    return createSafeSelection();
                }
            };
        }
    }

    function overrideEventListeners() {
        // Override addEventListener to wrap problematic listeners
        const originalAddEventListener = EventTarget.prototype.addEventListener;

        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if ((type === 'contextmenu' || type === 'mousedown' || type === 'mouseup') && typeof listener === 'function') {
                // Wrap the listener to handle selection errors
                const safeListener = function(event) {
                    try {
                        // Check if selection is available before calling the original listener
                        const selection = window.getSelection();
                        if (selection && typeof selection.rangeCount !== 'undefined') {
                            return listener.call(this, event);
                        } else {
                            console.log('🛡️ Prevented event handler with null selection:', type);
                            return false;
                        }
                    } catch (error) {
                        console.log('🛡️ Event error prevented:', type, error.message);
                        return false;
                    }
                };

                return originalAddEventListener.call(this, type, safeListener, options);
            }

            return originalAddEventListener.call(this, type, listener, options);
        };
    }

    function protectTinyMCE() {
        if (window.tinymce) {
            // Override TinyMCE selection methods
            tinymce.on('AddEditor', function(e) {
                const editor = e.editor;

                editor.on('init', function() {
                    console.log('🛡️ TinyMCE editor protected from selection errors');

                    // Override editor's selection methods
                    if (editor.selection && editor.selection.getSel) {
                        const originalGetSel = editor.selection.getSel;
                        editor.selection.getSel = function() {
                            try {
                                const sel = originalGetSel.call(this);
                                return sel || createSafeSelection();
                            } catch (error) {
                                console.log('🛡️ TinyMCE selection error handled:', error.message);
                                return createSafeSelection();
                            }
                        };
                    }
                });
            });
        }
    }

    function createSafeSelection() {
        return {
            rangeCount: 0,
            anchorNode: null,
            anchorOffset: 0,
            focusNode: null,
            focusOffset: 0,
            isCollapsed: true,
            type: 'None',
            getRangeAt: function(index) {
                return document.createRange();
            },
            removeAllRanges: function() {
                console.log('🛡️ Safe removeAllRanges called');
            },
            addRange: function(range) {
                console.log('🛡️ Safe addRange called');
            },
            collapse: function(node, offset) {
                console.log('🛡️ Safe collapse called');
            },
            extend: function(node, offset) {
                console.log('🛡️ Safe extend called');
            },
            toString: function() {
                return '';
            }
        };
    }

    function wrapSelection(selection) {
        if (!selection) return createSafeSelection();

        // Create a proxy to safely handle property access
        return new Proxy(selection, {
            get: function(target, prop) {
                try {
                    if (prop === 'rangeCount') {
                        return target.rangeCount || 0;
                    }

                    const value = target[prop];
                    if (typeof value === 'function') {
                        return function(...args) {
                            try {
                                return value.apply(target, args);
                            } catch (error) {
                                console.log(`🛡️ Selection method ${prop} error handled:`, error.message);
                                return prop === 'getRangeAt' ? document.createRange() : undefined;
                            }
                        };
                    }

                    return value;
                } catch (error) {
                    console.log(`🛡️ Selection property ${prop} error handled:`, error.message);
                    return prop === 'rangeCount' ? 0 : undefined;
                }
            }
        });
    }

    // Additional protection for document.getSelection
    if (document.getSelection) {
        const originalDocGetSelection = document.getSelection;
        document.getSelection = function() {
            try {
                const selection = originalDocGetSelection.call(document);
                return selection ? wrapSelection(selection) : createSafeSelection();
            } catch (error) {
                console.log('🛡️ document.getSelection error handled:', error.message);
                return createSafeSelection();
            }
        };
    }

    // Prevent unhandled promise rejections related to selection
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message) {
            const message = event.reason.message;
            if (problematicPatterns.some(pattern => pattern.test(message))) {
                console.log('🛡️ Selection promise rejection prevented:', message);
                event.preventDefault();
                return true;
            }
        }
        return false;
    });

    console.log('✅ Advanced Selection Error Fix initialized');

})();
