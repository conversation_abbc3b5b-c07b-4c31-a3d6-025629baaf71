/**
 * Selection Error Fix for TinyMCE and Context Menu Issues
 * Prevents "can't access property rangeCount, selection is null" errors
 */

(function() {
    'use strict';
    
    console.log('🛡️ Selection Error Fix loaded');
    
    // Override the problematic contextmenuhlpr.js behavior
    window.addEventListener('DOMContentLoaded', function() {
        // Wait for TinyMCE to load
        setTimeout(function() {
            // Override any existing problematic selection handlers
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                if (type === 'contextmenu' && typeof listener === 'function') {
                    // Wrap the listener to handle selection errors
                    const safeListener = function(event) {
                        try {
                            // Check if selection is available before calling the original listener
                            const selection = window.getSelection();
                            if (selection && typeof selection.rangeCount !== 'undefined') {
                                return listener.call(this, event);
                            } else {
                                console.log('🛡️ Prevented contextmenu handler with null selection');
                                return false;
                            }
                        } catch (error) {
                            console.log('🛡️ Contextmenu error prevented:', error.message);
                            return false;
                        }
                    };
                    
                    return originalAddEventListener.call(this, type, safeListener, options);
                }
                
                return originalAddEventListener.call(this, type, listener, options);
            };
            
            // Also handle mousedown events that might trigger selection issues
            document.addEventListener('mousedown', function(event) {
                try {
                    // Ensure selection is valid before any operations
                    const selection = window.getSelection();
                    if (!selection) {
                        console.log('🛡️ Mousedown with null selection prevented');
                        return;
                    }
                } catch (error) {
                    console.log('🛡️ Mousedown selection error prevented:', error.message);
                }
            }, true);
            
            // Handle TinyMCE specific issues
            if (window.tinymce) {
                tinymce.on('AddEditor', function(e) {
                    const editor = e.editor;
                    
                    editor.on('init', function() {
                        console.log('🛡️ TinyMCE editor initialized with selection fix');
                        
                        // Override editor's selection methods
                        const originalGetSelection = editor.selection.getSel;
                        if (originalGetSelection) {
                            editor.selection.getSel = function() {
                                try {
                                    const sel = originalGetSelection.call(this);
                                    if (!sel) {
                                        return {
                                            rangeCount: 0,
                                            getRangeAt: function() { return document.createRange(); },
                                            removeAllRanges: function() {},
                                            addRange: function() {}
                                        };
                                    }
                                    return sel;
                                } catch (error) {
                                    console.log('🛡️ TinyMCE selection error handled:', error.message);
                                    return {
                                        rangeCount: 0,
                                        getRangeAt: function() { return document.createRange(); },
                                        removeAllRanges: function() {},
                                        addRange: function() {}
                                    };
                                }
                            };
                        }
                    });
                });
            }
            
        }, 1000);
    });
    
    // Global error handler for selection-related errors
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message;
            
            if (message.includes('rangeCount') || 
                message.includes('selection is null') || 
                message.includes('contextmenuhlpr')) {
                
                console.log('🛡️ Selection error prevented globally:', message);
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        }
        return false;
    }, true);
    
    // Prevent unhandled promise rejections related to selection
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message) {
            const message = event.reason.message;
            
            if (message.includes('rangeCount') || 
                message.includes('selection is null')) {
                
                console.log('🛡️ Selection promise rejection prevented:', message);
                event.preventDefault();
                return true;
            }
        }
        return false;
    });
    
    console.log('✅ Selection Error Fix initialized');
    
})();
