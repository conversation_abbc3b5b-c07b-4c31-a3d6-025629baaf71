<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الذكاء الاصطناعي - لوحة التحكم</title>
    <link href="../css/ai-settings.css" rel="stylesheet">
    <style>
        body {
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <div class="ai-settings-container">
        <header class="ai-settings-header">
            <h1>إعدادات الذكاء الاصطناعي 🤖</h1>
        </header>

        <div class="api-section">
            <div class="api-section-header">
                <h2>OpenAI API</h2>
                <svg class="api-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.0198 1.1698a.0719.0719 0 0 1 .0337.0615v5.5366a4.504 4.504 0 0 1-4.49 4.5297z"/>
                </svg>
            </div>
            <div class="form-group">
                <label class="form-label">OpenAI API مفتاح</label>
                <input type="password" class="form-input" value="••••••••••••••••" id="openaiKey">
                <span class="status-badge active">مفعل</span>
            </div>
        </div>

        <div class="api-section">
            <div class="api-section-header">
                <h2>Anthropic Claude</h2>
                <div class="api-icon">🤖</div>
            </div>
            <div class="form-group">
                <label class="form-label">Anthropic API مفتاح</label>
                <input type="password" class="form-input" placeholder="أدخل مفتاح API" id="anthropicKey">
                <span class="status-badge inactive">غير مفعل</span>
            </div>
        </div>

        <div class="api-section">
            <div class="api-section-header">
                <h2>Google Gemini</h2>
                <div class="api-icon">🔮</div>
            </div>
            <div class="form-group">
                <label class="form-label">Gemini API مفتاح</label>
                <input type="password" class="form-input" placeholder="أدخل مفتاح API" id="geminiKey">
                <span class="status-badge inactive">غير مفعل</span>
            </div>
        </div>

        <div class="ai-features">
            <div class="feature-card">
                <h3>تحسين محركات البحث تلقائياً</h3>
                <p>إنشاء وصف مناسب لمحركات البحث تلقائياً للمنتجات الجديدة.</p>
                <button class="action-button primary-button">تفعيل</button>
            </div>

            <div class="feature-card">
                <h3>تحليل سجلات البحث</h3>
                <p>تحليل تفضيلات المستخدمين لتحسين المحتوى المقترح.</p>
                <button class="action-button secondary-button">إعداد</button>
            </div>

            <div class="feature-card">
                <h3>تحسين توصيف المنتجات</h3>
                <p>إنشاء نصوص وصفية جذابة للمنتجات باستخدام الذكاء الاصطناعي.</p>
                <button class="action-button primary-button">تفعيل</button>
            </div>

            <div class="feature-card">
                <h3>تحسين الترجمة</h3>
                <p>ترجمة وتحسين محتوى المنتجات تلقائياً باستخدام الذكاء الاصطناعي.</p>
                <button class="action-button secondary-button">إعداد</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle API key visibility toggle
            document.querySelectorAll('.form-input[type="password"]').forEach(input => {
                const toggleBtn = document.createElement('button');
                toggleBtn.className = 'toggle-visibility';
                toggleBtn.innerHTML = '👁️';
                toggleBtn.onclick = () => {
                    input.type = input.type === 'password' ? 'text' : 'password';
                };
                input.parentNode.insertBefore(toggleBtn, input.nextSibling);
            });

            // Handle form submission
            document.querySelector('.ai-settings-container').addEventListener('submit', function(e) {
                e.preventDefault();
                // Add your form submission logic here
            });
        });
    </script>
</body>
</html>
