/**
 * AI Settings Management for Mossaab Landing Page Admin
 * Handles AI configuration and testing
 */

let currentConfig = null;

document.addEventListener('DOMContentLoaded', function() {
    loadAISettings();
    setupEventListeners();
});

/**
 * Load AI settings from server
 */
async function loadAISettings() {
    try {
        const response = await safeApiCall('/php/api/ai.php?action=get_config');

        if (response.success) {
            currentConfig = response.data.config;
            updateUI(response.data);
            updateStatusAlert(response.data);
        } else {
            // Handle AI not configured case
            if (response.error && response.error.code === 'AI_NOT_CONFIGURED') {
                console.log('AI not configured, showing default settings');
                showDefaultSettings();
            } else {
                showError('فشل في تحميل إعدادات الذكاء الاصطناعي: ' + (response.message || response.error?.message || 'خطأ غير معروف'));
            }
        }
    } catch (error) {
        console.error('Error loading AI settings:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

function showDefaultSettings() {
    // Show default empty settings when AI is not configured
    const defaultData = {
        config: {
            openai_api_key: '',
            anthropic_api_key: '',
            google_api_key: '',
            default_provider: 'openai'
        },
        is_available: false,
        available_providers: []
    };
    updateUI(defaultData);
    updateStatusAlert(defaultData);
}

/**
 * Update UI with current settings
 */
function updateUI(data) {
    const config = data.config;

    // Update OpenAI settings
    document.getElementById('openaiEnabled').checked = config.providers.openai.enabled;
    document.getElementById('openaiApiKey').value = config.providers.openai.api_key || '';
    document.getElementById('openaiModel').value = config.providers.openai.model;
    updateProviderCard('openai', config.providers.openai.enabled);

    // Update Anthropic settings
    document.getElementById('anthropicEnabled').checked = config.providers.anthropic.enabled;
    document.getElementById('anthropicApiKey').value = config.providers.anthropic.api_key || '';
    document.getElementById('anthropicModel').value = config.providers.anthropic.model;
    updateProviderCard('anthropic', config.providers.anthropic.enabled);

    // Update Gemini settings
    document.getElementById('geminiEnabled').checked = config.providers.gemini.enabled;
    document.getElementById('geminiApiKey').value = config.providers.gemini.api_key || '';
    document.getElementById('geminiModel').value = config.providers.gemini.model;
    updateProviderCard('gemini', config.providers.gemini.enabled);
}

/**
 * Update provider card appearance
 */
function updateProviderCard(provider, enabled) {
    const card = document.getElementById(`${provider}Card`);
    const status = document.getElementById(`${provider}Status`);

    if (enabled) {
        card.className = 'ai-provider-card enabled';
        status.className = 'status-badge status-enabled';
        status.textContent = 'مفعل';
    } else {
        card.className = 'ai-provider-card disabled';
        status.className = 'status-badge status-disabled';
        status.textContent = 'غير مفعل';
    }
}

/**
 * Update status alert
 */
function updateStatusAlert(data) {
    const alert = document.getElementById('aiStatusAlert');
    const text = document.getElementById('aiStatusText');

    if (data.is_available) {
        alert.className = 'alert alert-success';
        text.innerHTML = `<i class="fas fa-check-circle"></i> الذكاء الاصطناعي متاح - المزودين المفعلين: ${data.available_providers.join(', ')}`;
    } else {
        alert.className = 'alert alert-warning';
        text.innerHTML = '<i class="fas fa-exclamation-triangle"></i> الذكاء الاصطناعي غير متاح - يرجى تكوين مزود واحد على الأقل';
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Provider enable/disable toggles
    document.getElementById('openaiEnabled').addEventListener('change', function() {
        updateProviderCard('openai', this.checked);
    });

    document.getElementById('anthropicEnabled').addEventListener('change', function() {
        updateProviderCard('anthropic', this.checked);
    });

    document.getElementById('geminiEnabled').addEventListener('change', function() {
        updateProviderCard('gemini', this.checked);
    });
}

/**
 * Test connection to AI provider
 */
async function testConnection(provider) {
    const button = event.target;
    const originalText = button.innerHTML;

    // Get API key from the form
    const apiKeyInput = document.querySelector(`input[placeholder*="${provider === 'openai' ? 'sk-' : provider === 'anthropic' ? 'sk-ant-' : 'AIza'}"]`);
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    // Validate API key is provided
    if (!apiKey) {
        showError(`يرجى إدخال مفتاح API لـ ${getProviderDisplayName(provider)} أولاً`);
        return;
    }

    // Validate API key format
    if (!validateApiKeyFormat(provider, apiKey)) {
        showError(`تنسيق مفتاح API غير صالح لـ ${getProviderDisplayName(provider)}`);
        return;
    }

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

    try {
        // Send POST request with API key
        const response = await fetch('../php/api/ai.php?action=test_connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                provider: provider,
                api_key: apiKey
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`✅ تم الاتصال بـ ${getProviderDisplayName(provider)} بنجاح!`);
            if (result.data && result.data.response) {
                console.log(`Test response from ${provider}:`, result.data.response);
            }
        } else {
            const errorMsg = result.data && result.data.error ? result.data.error : result.message;
            showError(`❌ فشل الاتصال بـ ${getProviderDisplayName(provider)}: ${errorMsg}`);
        }
    } catch (error) {
        console.error(`Error testing ${provider}:`, error);
        showError(`❌ خطأ في اختبار الاتصال بـ ${getProviderDisplayName(provider)}: ${error.message}`);
    } finally {
        // Restore button
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

/**
 * Get display name for provider
 */
function getProviderDisplayName(provider) {
    const names = {
        'openai': 'OpenAI',
        'anthropic': 'Anthropic Claude',
        'gemini': 'Google Gemini'
    };
    return names[provider] || provider;
}

/**
 * Validate API key format
 */
function validateApiKeyFormat(provider, apiKey) {
    switch (provider) {
        case 'openai':
            return /^sk-[a-zA-Z0-9\-_]{20,}$/.test(apiKey) || /^sk-proj-[a-zA-Z0-9\-_]{20,}$/.test(apiKey);
        case 'anthropic':
            return /^sk-ant-[a-zA-Z0-9\-_]{95,}$/.test(apiKey);
        case 'gemini':
            return /^AIza[a-zA-Z0-9\-_]{35}$/.test(apiKey);
        default:
            return false;
    }
}

/**
 * Save AI settings
 */
async function saveSettings() {
    try {
        // Initialize currentConfig if it's null
        if (!currentConfig) {
            currentConfig = {
                providers: {
                    openai: { max_tokens: 4000, temperature: 0.7 },
                    anthropic: { max_tokens: 4000, temperature: 0.7 },
                    google: { max_tokens: 4000, temperature: 0.7 }
                },
                default_provider: 'openai'
            };
        }

        // Collect form data
        const newConfig = {
            providers: {
                openai: {
                    enabled: document.getElementById('openaiEnabled').checked,
                    api_key: document.getElementById('openaiApiKey').value,
                    model: document.getElementById('openaiModel').value,
                    max_tokens: currentConfig.providers?.openai?.max_tokens || 4000,
                    temperature: currentConfig.providers?.openai?.temperature || 0.7
                },
                anthropic: {
                    enabled: document.getElementById('anthropicEnabled').checked,
                    api_key: document.getElementById('anthropicApiKey').value,
                    model: document.getElementById('anthropicModel').value,
                    max_tokens: currentConfig.providers?.anthropic?.max_tokens || 4000,
                    temperature: currentConfig.providers?.anthropic?.temperature || 0.7
                },
                gemini: {
                    enabled: document.getElementById('geminiEnabled').checked,
                    api_key: document.getElementById('geminiApiKey').value,
                    model: document.getElementById('geminiModel').value,
                    max_tokens: currentConfig.providers?.gemini?.max_tokens || 4000,
                    temperature: currentConfig.providers?.gemini?.temperature || 0.7
                }
            },
            default_provider: currentConfig.default_provider || 'openai',
            language: currentConfig.language || 'ar',
            prompts: currentConfig.prompts || {},
            features: currentConfig.features || {}
        };

        // Show loading state
        const saveButton = event.target;
        const originalText = saveButton.innerHTML;
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

        const response = await safeApiCall('/php/api/ai.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'update_config',
                config: newConfig
            })
        });

        if (response.success) {
            showSuccess('تم حفظ الإعدادات بنجاح!');

            // Update status
            updateStatusAlert({
                is_available: response.data.is_available,
                available_providers: response.data.available_providers
            });

            // Refresh AI Magic Wand if available
            if (window.aiMagicWand) {
                await window.aiMagicWand.refresh();
            }
        } else {
            showError(`فشل في حفظ الإعدادات: ${response.message}`);
        }

        // Restore button
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;

    } catch (error) {
        console.error('Error saving settings:', error);
        showError('خطأ في حفظ الإعدادات');
    }
}

/**
 * Show success message
 */
function showSuccess(message) {
    // Remove any existing alerts first
    removeExistingAlerts();

    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show ai-alert';
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2" style="font-size: 1.2em; color: #0f5132;"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Enhanced styling for better visibility and RTL support
    alert.style.cssText = `
        margin-bottom: 1rem;
        direction: rtl;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
        border-radius: 8px;
        font-weight: 500;
        animation: slideInDown 0.3s ease-out;
    `;

    const container = document.querySelector('main');
    container.insertBefore(alert, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => alert.remove(), 300);
        }
    }, 5000);
}

/**
 * Show error message
 */
function showError(message) {
    // Remove any existing alerts first
    removeExistingAlerts();

    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show ai-alert';
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-2" style="font-size: 1.2em; color: #842029;"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Enhanced styling for better visibility and RTL support
    alert.style.cssText = `
        margin-bottom: 1rem;
        direction: rtl;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
        border-radius: 8px;
        font-weight: 500;
        animation: slideInDown 0.3s ease-out;
    `;

    const container = document.querySelector('main');
    container.insertBefore(alert, container.firstChild);

    // Auto dismiss after 8 seconds (longer for errors)
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => alert.remove(), 300);
        }
    }, 8000);
}

/**
 * Remove existing alert messages
 */
function removeExistingAlerts() {
    const existingAlerts = document.querySelectorAll('.ai-alert');
    existingAlerts.forEach(alert => {
        alert.style.animation = 'slideOutUp 0.3s ease-in';
        setTimeout(() => alert.remove(), 300);
    });
}

/**
 * Toggle password visibility for API key inputs
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
        button.title = 'إخفاء كلمة المرور';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
        button.title = 'إظهار كلمة المرور';
    }
}

/**
 * Update model information display
 */
function updateModelInfo(provider, modelValue) {
    const modelInfoElement = document.getElementById(`${provider}ModelInfo`);
    const selectedOption = document.querySelector(`#${provider}Model option[value="${modelValue}"]`);

    if (!selectedOption || !modelInfoElement) return;

    const cost = selectedOption.getAttribute('data-cost');
    const description = selectedOption.getAttribute('data-description');

    let costBadgeClass = '';
    let costText = '';

    switch (cost) {
        case 'budget':
            costBadgeClass = 'budget';
            costText = 'اقتصادي';
            break;
        case 'balanced':
            costBadgeClass = 'balanced';
            costText = 'متوازن';
            break;
        case 'premium':
            costBadgeClass = 'premium';
            costText = 'متقدم';
            break;
        default:
            costBadgeClass = 'balanced';
            costText = 'عادي';
    }

    modelInfoElement.innerHTML = `
        <div class="model-cost-badge ${costBadgeClass}">${costText}</div>
        <div class="model-description">${description}</div>
    `;
}

/**
 * Update enabled providers count
 */
function updateEnabledProvidersCount() {
    const enabledCount = ['openai', 'anthropic', 'gemini'].filter(provider => {
        const checkbox = document.getElementById(`${provider}Enabled`);
        return checkbox && checkbox.checked;
    }).length;

    const countElement = document.getElementById('enabledProvidersCount');
    if (countElement) {
        countElement.textContent = enabledCount;
    }
}

/**
 * Enhanced update provider status function
 */
function updateProviderStatus(provider, enabled) {
    const statusElement = document.getElementById(`${provider}Status`);
    if (statusElement) {
        statusElement.textContent = enabled ? 'مفعل' : 'غير مفعل';
        statusElement.className = enabled ? 'status-badge ai-status-enabled' : 'status-badge ai-status-disabled';
    }

    // Update the enabled providers count
    updateEnabledProvidersCount();

    // Add visual feedback to the card
    const card = document.getElementById(`${provider}Card`);
    if (card) {
        if (enabled) {
            card.style.borderColor = '#28a745';
            card.style.boxShadow = '0 8px 32px rgba(40, 167, 69, 0.15)';
        } else {
            card.style.borderColor = '#e9ecef';
            card.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.08)';
        }
    }
}

/**
 * Initialize enhanced AI settings
 */
function initializeEnhancedAISettings() {
    // Initialize model info displays
    ['openai', 'anthropic', 'gemini'].forEach(provider => {
        const modelSelect = document.getElementById(`${provider}Model`);
        if (modelSelect && modelSelect.value) {
            updateModelInfo(provider, modelSelect.value);
        }
    });

    // Update enabled providers count
    updateEnabledProvidersCount();

    // Add event listeners for checkboxes
    ['openai', 'anthropic', 'gemini'].forEach(provider => {
        const checkbox = document.getElementById(`${provider}Enabled`);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                updateProviderStatus(provider, this.checked);
            });
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedAISettings();
});
