/**
 * Enhanced User Management System
 * Handles user CRUD operations, roles, permissions, and multi-user subscription system
 */

// Users data - will be loaded from API
let users = [];
let currentPage = 1;
let usersPerPage = 10;
let filteredUsers = [];
let selectedUsers = new Set();
let editingUserId = null;
let userManagementLoaded = false;

// User roles and permissions configuration
const USER_ROLES = {
    'super_admin': {
        name: 'مدير عام',
        permissions: ['all'],
        color: 'danger',
        icon: 'fas fa-crown'
    },
    'admin': {
        name: 'مدير',
        permissions: ['users', 'products', 'orders', 'settings'],
        color: 'warning',
        icon: 'fas fa-user-shield'
    },
    'store_owner': {
        name: 'صاحب متجر',
        permissions: ['own_store', 'products', 'orders'],
        color: 'info',
        icon: 'fas fa-store'
    },
    'customer': {
        name: 'عميل',
        permissions: ['profile', 'orders'],
        color: 'success',
        icon: 'fas fa-user'
    }
};

// Subscription limits configuration
const SUBSCRIPTION_LIMITS = {
    'free': {
        name: 'مجاني',
        products: 5,
        landing_pages: 2,
        storage: 100, // MB
        templates: 3
    },
    'basic': {
        name: 'أساسي',
        products: 50,
        landing_pages: 10,
        storage: 1000, // MB
        templates: 10
    },
    'premium': {
        name: 'مميز',
        products: 500,
        landing_pages: 50,
        storage: 5000, // MB
        templates: 50
    },
    'unlimited': {
        name: 'غير محدود',
        products: -1, // unlimited
        landing_pages: -1,
        storage: -1,
        templates: -1
    }
};

/**
 * Initialize enhanced user management
 */
function initializeUserManagement() {
    console.log('👥 Initializing Enhanced User Management...');

    if (userManagementLoaded) {
        console.log('User management already loaded');
        return;
    }

    // Load users from API
    loadUsersFromAPI();

    // Add event listeners
    addEventListeners();

    // Initialize advanced features
    initializeAdvancedFeatures();

    userManagementLoaded = true;
    console.log('✅ Enhanced User Management initialized successfully');
}

/**
 * Load users from API
 */
async function loadUsersFromAPI() {
    try {
        showLoadingState();

        const response = await fetch('../php/api/users.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            users = data.users || [];
            filteredUsers = [...users];

            // Update UI
            updateUserSummary();
            loadUsers();
        } else {
            throw new Error(data.message || 'Failed to load users');
        }

    } catch (error) {
        console.error('Error loading users:', error);
        showErrorState('خطأ في تحميل المستخدمين: ' + error.message);

        // Load sample data as fallback
        loadSampleUsers();
    }
}

/**
 * Load sample users as fallback
 */
function loadSampleUsers() {
    users = [
        {
            id: 1,
            name: 'أحمد محمد',
            email: '<EMAIL>',
            role: 'admin',
            status: 'active',
            avatar: 'https://via.placeholder.com/40',
            phone: '+213 555 123 456',
            registeredAt: '2024-01-15',
            lastLogin: '2024-01-20 14:30',
            notes: 'مدير النظام الرئيسي',
            subscription: 'unlimited',
            store_id: 1
        },
        {
            id: 2,
            name: 'فاطمة علي',
            email: '<EMAIL>',
            role: 'store_owner',
            status: 'active',
            avatar: 'https://via.placeholder.com/40',
            phone: '+213 555 789 012',
            registeredAt: '2024-01-10',
            lastLogin: '2024-01-19 09:15',
            notes: 'صاحبة متجر إلكتروني',
            subscription: 'premium',
            store_id: 2
        },
        {
            id: 3,
            name: 'محمد حسن',
            email: '<EMAIL>',
            role: 'customer',
            status: 'inactive',
            avatar: 'https://via.placeholder.com/40',
            phone: '+213 555 345 678',
            registeredAt: '2024-01-05',
            lastLogin: '2024-01-18 16:45',
            notes: 'عميل مميز',
            subscription: 'basic',
            store_id: null
        }
    ];

    filteredUsers = [...users];
    updateUserSummary();
    loadUsers();
}

/**
 * Initialize advanced features
 */
function initializeAdvancedFeatures() {
    // Initialize bulk actions
    initializeBulkActions();

    // Initialize user analytics
    initializeUserAnalytics();

    // Initialize subscription management
    initializeSubscriptionManagement();

    // Initialize role-based access control
    initializeRoleBasedAccess();
}

/**
 * Initialize bulk actions
 */
function initializeBulkActions() {
    const bulkActionSelect = document.getElementById('bulkAction');
    const applyBulkBtn = document.getElementById('applyBulkAction');

    if (applyBulkBtn) {
        applyBulkBtn.addEventListener('click', handleBulkAction);
    }
}

/**
 * Initialize user analytics
 */
function initializeUserAnalytics() {
    // This would initialize charts and analytics
    console.log('User analytics initialized');
}

/**
 * Initialize subscription management
 */
function initializeSubscriptionManagement() {
    // Initialize subscription-related functionality
    console.log('Subscription management initialized');
}

/**
 * Initialize role-based access control
 */
function initializeRoleBasedAccess() {
    // Initialize RBAC functionality
    console.log('Role-based access control initialized');
}

/**
 * Update enhanced user summary statistics
 */
function updateUserSummary() {
    const totalUsers = users.length;
    const activeUsers = users.filter(user => user.status === 'active').length;
    const adminUsers = users.filter(user => user.role === 'admin' || user.role === 'super_admin').length;
    const storeOwners = users.filter(user => user.role === 'store_owner').length;
    const customers = users.filter(user => user.role === 'customer').length;

    // Update basic statistics
    const totalUsersEl = document.getElementById('totalUsers');
    const activeUsersEl = document.getElementById('activeUsers');
    const adminUsersEl = document.getElementById('adminUsers');

    if (totalUsersEl) totalUsersEl.textContent = totalUsers;
    if (activeUsersEl) activeUsersEl.textContent = activeUsers;
    if (adminUsersEl) adminUsersEl.textContent = adminUsers;

    // Update additional statistics
    const storeOwnersEl = document.getElementById('storeOwners');
    const customersEl = document.getElementById('customers');
    const lastUpdatedEl = document.getElementById('lastUpdated');

    if (storeOwnersEl) storeOwnersEl.textContent = storeOwners;
    if (customersEl) customersEl.textContent = customers;
    if (lastUpdatedEl) lastUpdatedEl.textContent = new Date().toLocaleString('ar-DZ');

    // Update subscription statistics
    updateSubscriptionStatistics();
}

/**
 * Update subscription statistics
 */
function updateSubscriptionStatistics() {
    const subscriptionCounts = {};

    // Count users by subscription type
    users.forEach(user => {
        const subscription = user.subscription || 'free';
        subscriptionCounts[subscription] = (subscriptionCounts[subscription] || 0) + 1;
    });

    // Update subscription elements
    Object.keys(SUBSCRIPTION_LIMITS).forEach(type => {
        const element = document.getElementById(`${type}Subscribers`);
        if (element) {
            element.textContent = subscriptionCounts[type] || 0;
        }
    });
}

/**
 * Load and display users
 */
function loadUsers() {
    // Apply filters
    applyFilters();

    // Calculate pagination
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const pageUsers = filteredUsers.slice(startIndex, endIndex);

    // Display users
    displayUsers(pageUsers);

    // Update pagination
    updatePagination(totalPages);

    // Update users count
    document.getElementById('usersCount').textContent = `${filteredUsers.length} مستخدم`;
}

/**
 * Apply search and filter criteria
 */
function applyFilters() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredUsers = users.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                            user.email.toLowerCase().includes(searchTerm);
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesStatus = !statusFilter || user.status === statusFilter;

        return matchesSearch && matchesRole && matchesStatus;
    });

    // Reset to first page when filters change
    currentPage = 1;
}

/**
 * Display users in table
 */
function displayUsers(pageUsers) {
    const tbody = document.getElementById('usersTableBody');

    if (pageUsers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 2rem; color: #6c757d;">
                    <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>لا توجد مستخدمين مطابقين للبحث</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = pageUsers.map(user => `
        <tr class="user-row" data-user-id="${user.id}">
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.id}"
                       onchange="toggleUserSelection(${user.id}, this.checked)">
            </td>
            <td>
                <div class="user-avatar-container">
                    <img src="${user.avatar || 'https://via.placeholder.com/40'}" alt="${user.name}"
                         class="user-avatar">
                    <div class="user-status-indicator ${user.status}"></div>
                </div>
            </td>
            <td>
                <div class="user-info">
                    <div class="user-name">${user.name}</div>
                    <div class="user-id">ID: ${user.id}</div>
                    ${user.phone ? `<div class="user-phone"><i class="fas fa-phone"></i> ${user.phone}</div>` : ''}
                </div>
            </td>
            <td>
                <div class="user-email">${user.email}</div>
                ${user.store_id ? `<div class="user-store">متجر #${user.store_id}</div>` : ''}
            </td>
            <td>
                <div class="role-container">
                    <span class="badge badge-${getRoleBadgeClass(user.role)}">
                        <i class="${USER_ROLES[user.role]?.icon || 'fas fa-user'}"></i>
                        ${getRoleDisplayName(user.role)}
                    </span>
                </div>
            </td>
            <td>
                <div class="subscription-container">
                    <span class="badge badge-subscription badge-${getSubscriptionBadgeClass(user.subscription)}">
                        ${getSubscriptionDisplayName(user.subscription)}
                    </span>
                    <div class="subscription-limits">
                        ${getSubscriptionLimitsDisplay(user.subscription)}
                    </div>
                </div>
            </td>
            <td>
                <span class="badge badge-${getStatusBadgeClass(user.status)}">
                    ${getStatusDisplayName(user.status)}
                </span>
            </td>
            <td>
                <div class="date-info">
                    <div class="registered-date">${formatDate(user.registeredAt)}</div>
                    <div class="last-login">${user.lastLogin ? formatDateTime(user.lastLogin) : 'لم يسجل دخول'}</div>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="viewUser(${user.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editUser(${user.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="manageSubscription(${user.id})" title="إدارة الاشتراك">
                        <i class="fas fa-crown"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * Get role badge CSS class
 */
function getRoleBadgeClass(role) {
    const classes = {
        'admin': 'danger',
        'editor': 'warning',
        'customer': 'info'
    };
    return classes[role] || 'secondary';
}

/**
 * Get role display name
 */
function getRoleDisplayName(role) {
    const names = {
        'admin': 'مدير',
        'editor': 'محرر',
        'customer': 'عميل'
    };
    return names[role] || role;
}

/**
 * Get status badge CSS class
 */
function getStatusBadgeClass(status) {
    const classes = {
        'active': 'success',
        'inactive': 'secondary',
        'suspended': 'danger'
    };
    return classes[status] || 'secondary';
}

/**
 * Get status display name
 */
function getStatusDisplayName(status) {
    const names = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'suspended': 'معلق'
    };
    return names[status] || status;
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Update pagination controls
 */
function updatePagination(totalPages) {
    const paginationInfo = document.getElementById('paginationInfo');
    const pageNumbers = document.getElementById('pageNumbers');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');

    // Update info
    const startIndex = (currentPage - 1) * usersPerPage + 1;
    const endIndex = Math.min(currentPage * usersPerPage, filteredUsers.length);
    paginationInfo.textContent = `عرض ${startIndex}-${endIndex} من ${filteredUsers.length}`;

    // Update buttons
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages;

    // Update page numbers
    pageNumbers.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const button = document.createElement('button');
            button.textContent = i;
            button.className = i === currentPage ? 'active' : '';
            button.onclick = () => goToPage(i);
            pageNumbers.appendChild(button);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const span = document.createElement('span');
            span.textContent = '...';
            span.style.padding = '0 0.5rem';
            pageNumbers.appendChild(span);
        }
    }
}

/**
 * Change page
 */
function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        loadUsers();
    }
}

/**
 * Go to specific page
 */
function goToPage(page) {
    currentPage = page;
    loadUsers();
}

/**
 * Toggle user selection
 */
function toggleUserSelection(userId, selected) {
    if (selected) {
        selectedUsers.add(userId);
    } else {
        selectedUsers.delete(userId);
    }

    updateBulkActions();
    updateSelectAllCheckbox();
}

/**
 * Toggle select all users
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        toggleUserSelection(parseInt(checkbox.value), checkbox.checked);
    });
}

/**
 * Update select all checkbox state
 */
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === userCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

/**
 * Update bulk actions visibility
 */
function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedUsers.size > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = selectedUsers.size;
    } else {
        bulkActions.style.display = 'none';
    }
}

/**
 * Add event listeners
 */
function addEventListeners() {
    // Search and filter inputs
    document.getElementById('userSearch').addEventListener('input', debounce(loadUsers, 300));
    document.getElementById('roleFilter').addEventListener('change', loadUsers);
    document.getElementById('statusFilter').addEventListener('change', loadUsers);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Show add user modal
 */
function showAddUserModal() {
    editingUserId = null;
    document.getElementById('modalTitle').textContent = 'إضافة مستخدم جديد';
    document.getElementById('userForm').reset();
    document.getElementById('userModal').classList.add('show');
}

/**
 * Close user modal
 */
function closeUserModal() {
    document.getElementById('userModal').classList.remove('show');
    editingUserId = null;
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    editingUserId = userId;
    document.getElementById('modalTitle').textContent = 'تعديل المستخدم';

    // Fill form with user data
    document.getElementById('userName').value = user.name;
    document.getElementById('userEmail').value = user.email;
    document.getElementById('userRole').value = user.role;
    document.getElementById('userStatus').value = user.status;
    document.getElementById('userPhone').value = user.phone || '';
    document.getElementById('userNotes').value = user.notes || '';

    document.getElementById('userModal').classList.add('show');
}

/**
 * Save user (add or edit)
 */
function saveUser() {
    const formData = {
        name: document.getElementById('userName').value,
        email: document.getElementById('userEmail').value,
        role: document.getElementById('userRole').value,
        status: document.getElementById('userStatus').value,
        phone: document.getElementById('userPhone').value,
        notes: document.getElementById('userNotes').value,
        password: document.getElementById('userPassword').value
    };

    // Validate required fields
    if (!formData.name || !formData.email || !formData.role) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (editingUserId) {
        // Update existing user
        const userIndex = users.findIndex(u => u.id === editingUserId);
        if (userIndex !== -1) {
            users[userIndex] = { ...users[userIndex], ...formData };
            showNotification('تم تحديث المستخدم بنجاح', 'success');
        }
    } else {
        // Add new user
        const newUser = {
            id: Math.max(...users.map(u => u.id)) + 1,
            ...formData,
            avatar: 'https://via.placeholder.com/40',
            registeredAt: new Date().toISOString().split('T')[0],
            lastLogin: null
        };
        users.push(newUser);
        showNotification('تم إضافة المستخدم بنجاح', 'success');
    }

    // Refresh display
    updateUserSummary();
    loadUsers();
    closeUserModal();
}

/**
 * View user details
 */
function viewUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const detailsContent = document.getElementById('userDetailsContent');
    detailsContent.innerHTML = `
        <div class="user-details">
            <div class="user-avatar-section">
                <img src="${user.avatar}" alt="${user.name}" style="width: 80px; height: 80px; border-radius: 50%;">
                <h4>${user.name}</h4>
                <span class="badge badge-${getStatusBadgeClass(user.status)}">${getStatusDisplayName(user.status)}</span>
            </div>
            <div class="user-info-grid">
                <div class="info-item">
                    <label>البريد الإلكتروني:</label>
                    <span>${user.email}</span>
                </div>
                <div class="info-item">
                    <label>الدور:</label>
                    <span class="badge badge-${getRoleBadgeClass(user.role)}">${getRoleDisplayName(user.role)}</span>
                </div>
                <div class="info-item">
                    <label>رقم الهاتف:</label>
                    <span>${user.phone || '--'}</span>
                </div>
                <div class="info-item">
                    <label>تاريخ التسجيل:</label>
                    <span>${formatDate(user.registeredAt)}</span>
                </div>
                <div class="info-item">
                    <label>آخر دخول:</label>
                    <span>${user.lastLogin || '--'}</span>
                </div>
                <div class="info-item">
                    <label>الملاحظات:</label>
                    <span>${user.notes || '--'}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('userDetailsModal').classList.add('show');
}

/**
 * Close user details modal
 */
function closeUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.remove('show');
}

/**
 * Delete user
 */
function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        users = users.filter(u => u.id !== userId);
        updateUserSummary();
        loadUsers();
        showNotification('تم حذف المستخدم بنجاح', 'success');
    }
}

/**
 * Bulk actions
 */
function bulkAction(action) {
    if (selectedUsers.size === 0) return;

    const selectedArray = Array.from(selectedUsers);
    let message = '';

    switch (action) {
        case 'activate':
            selectedArray.forEach(userId => {
                const user = users.find(u => u.id === userId);
                if (user) user.status = 'active';
            });
            message = 'تم تفعيل المستخدمين المحددين';
            break;

        case 'deactivate':
            selectedArray.forEach(userId => {
                const user = users.find(u => u.id === userId);
                if (user) user.status = 'inactive';
            });
            message = 'تم إلغاء تفعيل المستخدمين المحددين';
            break;

        case 'delete':
            if (confirm(`هل أنت متأكد من حذف ${selectedUsers.size} مستخدم؟`)) {
                users = users.filter(u => !selectedUsers.has(u.id));
                message = 'تم حذف المستخدمين المحددين';
            } else {
                return;
            }
            break;
    }

    // Clear selection
    selectedUsers.clear();
    updateBulkActions();

    // Refresh display
    updateUserSummary();
    loadUsers();
    showNotification(message, 'success');
}

/**
 * Export users
 */
function exportUsers() {
    const csvContent = [
        ['الاسم', 'البريد الإلكتروني', 'الدور', 'الحالة', 'الهاتف', 'تاريخ التسجيل'],
        ...users.map(user => [
            user.name,
            user.email,
            getRoleDisplayName(user.role),
            getStatusDisplayName(user.status),
            user.phone || '',
            user.registeredAt
        ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();

    showNotification('تم تصدير المستخدمين بنجاح', 'success');
}

/**
 * Toggle password visibility
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeUserManagement();
});
