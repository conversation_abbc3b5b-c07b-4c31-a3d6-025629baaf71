/**
 * Reports and Analytics System
 * Handles data visualization and reporting for the admin panel
 */

// Real data from database (will be loaded from API)
let reportsData = {
    sales: {
        daily: [],
        monthly: [],
        labels: []
    },
    products: {
        topSelling: []
    },
    customers: {
        total: 0,
        new: 0,
        returning: 0,
        growth: 0
    },
    orders: {
        total: 0,
        pending: 0,
        completed: 0,
        cancelled: 0,
        revenue: 0
    },
    summary: {
        sales: { total: 0, growth: 0 },
        orders: { total: 0, growth: 0 },
        customers: { total: 0, growth: 0 },
        products: { total: 0, growth: 0 }
    },
    recentActivity: []
};

/**
 * Initialize reports and analytics
 */
async function initializeReports() {
    console.log('Initializing reports and analytics...');

    try {
        // Load real data from API
        await loadReportsData();

        // Load reports content with real data
        loadReportsContent();

        console.log('Reports and analytics initialized successfully');
    } catch (error) {
        console.error('Error initializing reports:', error);
        showNotification('خطأ في تحميل بيانات التقارير', 'error');

        // Load content with empty data as fallback
        loadReportsContent();
    }
}

/**
 * Load reports data from API
 */
async function loadReportsData() {
    try {
        console.log('Loading reports data from API...');

        const response = await fetch('../php/api/reports.php?action=summary');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Failed to load reports data');
        }

        // Update reportsData with real data
        reportsData.summary = data.summary;
        reportsData.sales = data.sales;
        reportsData.orders = data.orders;
        reportsData.customers = data.customers;
        reportsData.products.topSelling = data.topProducts;
        reportsData.recentActivity = data.recentActivity;

        console.log('Reports data loaded successfully:', reportsData);

    } catch (error) {
        console.error('Error loading reports data:', error);
        throw error;
    }
}

/**
 * Load reports content
 */
function loadReportsContent() {
    const content = `
        <div class="reports-container">
            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card sales">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي المبيعات</h3>
                        <p class="card-value">${reportsData.summary.sales.total.toLocaleString()} دج</p>
                        <span class="card-change ${reportsData.summary.sales.growth >= 0 ? 'positive' : 'negative'}">
                            ${reportsData.summary.sales.growth >= 0 ? '+' : ''}${reportsData.summary.sales.growth}%
                        </span>
                    </div>
                </div>

                <div class="summary-card orders">
                    <div class="card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي الطلبات</h3>
                        <p class="card-value">${reportsData.summary.orders.total}</p>
                        <span class="card-change ${reportsData.summary.orders.growth >= 0 ? 'positive' : 'negative'}">
                            ${reportsData.summary.orders.growth >= 0 ? '+' : ''}${reportsData.summary.orders.growth}%
                        </span>
                    </div>
                </div>

                <div class="summary-card customers">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي العملاء</h3>
                        <p class="card-value">${reportsData.summary.customers.total}</p>
                        <span class="card-change ${reportsData.summary.customers.growth >= 0 ? 'positive' : 'negative'}">
                            ${reportsData.summary.customers.growth >= 0 ? '+' : ''}${reportsData.summary.customers.growth}%
                        </span>
                    </div>
                </div>

                <div class="summary-card products">
                    <div class="card-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="card-content">
                        <h3>المنتجات النشطة</h3>
                        <p class="card-value">${reportsData.summary.products.total}</p>
                        <span class="card-change ${reportsData.summary.products.growth >= 0 ? 'positive' : (reportsData.summary.products.growth === 0 ? 'neutral' : 'negative')}">
                            ${reportsData.summary.products.growth >= 0 ? '+' : ''}${reportsData.summary.products.growth}%
                        </span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-area"></i> مبيعات الأسبوع</h3>
                        <div class="chart-controls">
                            <select id="salesPeriod" class="form-control">
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-pie"></i> توزيع الطلبات</h3>
                    </div>
                    <div class="chart-content">
                        <canvas id="ordersChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Products Table -->
            <div class="reports-table-container">
                <div class="table-header">
                    <h3><i class="fas fa-trophy"></i> أفضل المنتجات مبيعاً</h3>
                    <button class="action-button" onclick="exportTopProducts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="reports-table">
                        <thead>
                            <tr>
                                <th>المرتبة</th>
                                <th>اسم المنتج</th>
                                <th>عدد المبيعات</th>
                                <th>الإيرادات</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody id="topProductsTable">
                            <!-- Top products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="activity-container">
                <div class="activity-header">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <button class="action-button secondary" onclick="refreshActivity()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
                <div class="activity-list" id="activityList">
                    <!-- Activity items will be loaded here -->
                </div>
            </div>

            <!-- Export Options -->
            <div class="export-section">
                <h3><i class="fas fa-file-export"></i> تصدير التقارير</h3>
                <div class="export-options">
                    <button class="action-button" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button class="action-button" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                    <button class="action-button" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv"></i> تصدير CSV
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('reportsContent').innerHTML = content;

    // Initialize charts and data
    initializeCharts();
    loadTopProducts();
    loadRecentActivity();
}

/**
 * Initialize charts
 */
function initializeCharts() {
    // Sales Chart
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        // Simple chart implementation (would use Chart.js in production)
        drawSimpleChart(salesCtx, reportsData.sales.daily, reportsData.sales.labels, 'line');
    }

    // Orders Chart
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
        const ordersData = [
            reportsData.orders.completed,
            reportsData.orders.pending,
            reportsData.orders.cancelled
        ];
        const ordersLabels = ['مكتملة', 'قيد الانتظار', 'ملغية'];
        drawSimpleChart(ordersCtx, ordersData, ordersLabels, 'pie');
    }
}

/**
 * Simple chart drawing function
 */
function drawSimpleChart(canvas, data, labels, type) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    if (type === 'line') {
        drawLineChart(ctx, data, labels, width, height);
    } else if (type === 'pie') {
        drawPieChart(ctx, data, labels, width, height);
    }
}

/**
 * Draw line chart
 */
function drawLineChart(ctx, data, labels, width, height) {
    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;

    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue;

    // Draw axes
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;

    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();

    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();

    // Draw line
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    ctx.beginPath();

    data.forEach((value, index) => {
        const x = padding + (index * chartWidth) / (data.length - 1);
        const y = height - padding - ((value - minValue) / range) * chartHeight;

        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });

    ctx.stroke();

    // Draw points
    ctx.fillStyle = '#667eea';
    data.forEach((value, index) => {
        const x = padding + (index * chartWidth) / (data.length - 1);
        const y = height - padding - ((value - minValue) / range) * chartHeight;

        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
    });
}

/**
 * Draw pie chart
 */
function drawPieChart(ctx, data, labels, width, height) {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 20;

    const total = data.reduce((sum, value) => sum + value, 0);
    const colors = ['#10b981', '#f59e0b', '#ef4444'];

    let currentAngle = -Math.PI / 2;

    data.forEach((value, index) => {
        const sliceAngle = (value / total) * 2 * Math.PI;

        ctx.fillStyle = colors[index % colors.length];
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fill();

        currentAngle += sliceAngle;
    });
}

/**
 * Load top products table
 */
function loadTopProducts() {
    const tbody = document.getElementById('topProductsTable');
    if (!tbody) return;

    const totalRevenue = reportsData.products.topSelling.reduce((sum, product) => sum + product.revenue, 0);

    tbody.innerHTML = reportsData.products.topSelling.map((product, index) => {
        const percentage = ((product.revenue / totalRevenue) * 100).toFixed(1);
        return `
            <tr>
                <td><span class="rank">${index + 1}</span></td>
                <td>${product.name}</td>
                <td>${product.sales}</td>
                <td>${product.revenue.toLocaleString()} دج</td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${percentage}%"></div>
                        <span class="progress-text">${percentage}%</span>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Load recent activity
 */
function loadRecentActivity() {
    const activityList = document.getElementById('activityList');
    if (!activityList) return;

    // Use real activity data from API
    const activities = reportsData.recentActivity || [];

    if (activities.length === 0) {
        activityList.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon neutral">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-message">لا توجد أنشطة حديثة</p>
                    <span class="activity-time">--</span>
                </div>
            </div>
        `;
        return;
    }

    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.type}">
                <i class="fas fa-${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <p class="activity-message">${activity.message}</p>
                <span class="activity-time">${activity.time}</span>
            </div>
        </div>
    `).join('');
}

/**
 * Export top products
 */
function exportTopProducts() {
    const data = reportsData.products.topSelling.map(product => ({
        'اسم المنتج': product.name,
        'عدد المبيعات': product.sales,
        'الإيرادات': product.revenue
    }));

    const csv = convertToCSV(data);
    downloadCSV(csv, 'top-products.csv');

    showNotification('تم تصدير قائمة أفضل المنتجات بنجاح', 'success');
}

/**
 * Export report
 */
function exportReport(format) {
    showNotification(`جاري تصدير التقرير بصيغة ${format.toUpperCase()}...`, 'info');

    // Simulate export process
    setTimeout(() => {
        showNotification(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
    }, 2000);
}

/**
 * Refresh activity
 */
async function refreshActivity() {
    showNotification('جاري تحديث النشاط الأخير...', 'info');

    try {
        // Reload activity data from API
        const response = await fetch('../php/api/reports.php?action=activity');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            reportsData.recentActivity = data.data;
            loadRecentActivity();
            showNotification('تم تحديث النشاط الأخير بنجاح', 'success');
        } else {
            throw new Error(data.message || 'Failed to refresh activity');
        }

    } catch (error) {
        console.error('Error refreshing activity:', error);
        showNotification('خطأ في تحديث النشاط الأخير', 'error');
    }
}

/**
 * Convert data to CSV
 */
function convertToCSV(data) {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    return csvContent;
}

/**
 * Download CSV file
 */
function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * Show notification
 */
function showNotification(message, type) {
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            case 'info':
                notificationManager.showInfo(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        alert(message);
    }
}

// Make functions globally available
window.initializeReports = initializeReports;
window.exportTopProducts = exportTopProducts;
window.exportReport = exportReport;
window.refreshActivity = refreshActivity;
