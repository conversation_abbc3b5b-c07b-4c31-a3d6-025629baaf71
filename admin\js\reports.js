/**
 * Reports and Analytics System
 * Handles data visualization and reporting for the admin panel
 */

// Sample data for demonstration
let reportsData = {
    sales: {
        daily: [120, 150, 180, 200, 170, 190, 220],
        monthly: [3500, 4200, 3800, 4500, 5100, 4800, 5300],
        labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    },
    products: {
        topSelling: [
            { name: 'منتج أ', sales: 150, revenue: 45000 },
            { name: 'منتج ب', sales: 120, revenue: 36000 },
            { name: 'منتج ج', sales: 100, revenue: 30000 },
            { name: 'منتج د', sales: 80, revenue: 24000 },
            { name: 'منتج هـ', sales: 60, revenue: 18000 }
        ]
    },
    customers: {
        total: 1250,
        new: 85,
        returning: 1165,
        growth: 12.5
    },
    orders: {
        total: 2340,
        pending: 45,
        completed: 2200,
        cancelled: 95,
        revenue: 156000
    }
};

/**
 * Initialize reports and analytics
 */
function initializeReports() {
    console.log('Initializing reports and analytics...');
    
    // Load reports content
    loadReportsContent();
    
    console.log('Reports and analytics initialized successfully');
}

/**
 * Load reports content
 */
function loadReportsContent() {
    const content = `
        <div class="reports-container">
            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card sales">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي المبيعات</h3>
                        <p class="card-value">${reportsData.orders.revenue.toLocaleString()} دج</p>
                        <span class="card-change positive">+12.5%</span>
                    </div>
                </div>
                
                <div class="summary-card orders">
                    <div class="card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي الطلبات</h3>
                        <p class="card-value">${reportsData.orders.total}</p>
                        <span class="card-change positive">+8.3%</span>
                    </div>
                </div>
                
                <div class="summary-card customers">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h3>إجمالي العملاء</h3>
                        <p class="card-value">${reportsData.customers.total}</p>
                        <span class="card-change positive">+${reportsData.customers.growth}%</span>
                    </div>
                </div>
                
                <div class="summary-card products">
                    <div class="card-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="card-content">
                        <h3>المنتجات النشطة</h3>
                        <p class="card-value">45</p>
                        <span class="card-change neutral">0%</span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-area"></i> مبيعات الأسبوع</h3>
                        <div class="chart-controls">
                            <select id="salesPeriod" class="form-control">
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-pie"></i> توزيع الطلبات</h3>
                    </div>
                    <div class="chart-content">
                        <canvas id="ordersChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Products Table -->
            <div class="reports-table-container">
                <div class="table-header">
                    <h3><i class="fas fa-trophy"></i> أفضل المنتجات مبيعاً</h3>
                    <button class="action-button" onclick="exportTopProducts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="reports-table">
                        <thead>
                            <tr>
                                <th>المرتبة</th>
                                <th>اسم المنتج</th>
                                <th>عدد المبيعات</th>
                                <th>الإيرادات</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody id="topProductsTable">
                            <!-- Top products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="activity-container">
                <div class="activity-header">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <button class="action-button secondary" onclick="refreshActivity()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
                <div class="activity-list" id="activityList">
                    <!-- Activity items will be loaded here -->
                </div>
            </div>

            <!-- Export Options -->
            <div class="export-section">
                <h3><i class="fas fa-file-export"></i> تصدير التقارير</h3>
                <div class="export-options">
                    <button class="action-button" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button class="action-button" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                    <button class="action-button" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv"></i> تصدير CSV
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('reportsContent').innerHTML = content;
    
    // Initialize charts and data
    initializeCharts();
    loadTopProducts();
    loadRecentActivity();
}

/**
 * Initialize charts
 */
function initializeCharts() {
    // Sales Chart
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        // Simple chart implementation (would use Chart.js in production)
        drawSimpleChart(salesCtx, reportsData.sales.daily, reportsData.sales.labels, 'line');
    }

    // Orders Chart
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
        const ordersData = [
            reportsData.orders.completed,
            reportsData.orders.pending,
            reportsData.orders.cancelled
        ];
        const ordersLabels = ['مكتملة', 'قيد الانتظار', 'ملغية'];
        drawSimpleChart(ordersCtx, ordersData, ordersLabels, 'pie');
    }
}

/**
 * Simple chart drawing function
 */
function drawSimpleChart(canvas, data, labels, type) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    if (type === 'line') {
        drawLineChart(ctx, data, labels, width, height);
    } else if (type === 'pie') {
        drawPieChart(ctx, data, labels, width, height);
    }
}

/**
 * Draw line chart
 */
function drawLineChart(ctx, data, labels, width, height) {
    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue;
    
    // Draw axes
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // Draw line
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    data.forEach((value, index) => {
        const x = padding + (index * chartWidth) / (data.length - 1);
        const y = height - padding - ((value - minValue) / range) * chartHeight;
        
        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });
    
    ctx.stroke();
    
    // Draw points
    ctx.fillStyle = '#667eea';
    data.forEach((value, index) => {
        const x = padding + (index * chartWidth) / (data.length - 1);
        const y = height - padding - ((value - minValue) / range) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
    });
}

/**
 * Draw pie chart
 */
function drawPieChart(ctx, data, labels, width, height) {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 20;
    
    const total = data.reduce((sum, value) => sum + value, 0);
    const colors = ['#10b981', '#f59e0b', '#ef4444'];
    
    let currentAngle = -Math.PI / 2;
    
    data.forEach((value, index) => {
        const sliceAngle = (value / total) * 2 * Math.PI;
        
        ctx.fillStyle = colors[index % colors.length];
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fill();
        
        currentAngle += sliceAngle;
    });
}

/**
 * Load top products table
 */
function loadTopProducts() {
    const tbody = document.getElementById('topProductsTable');
    if (!tbody) return;
    
    const totalRevenue = reportsData.products.topSelling.reduce((sum, product) => sum + product.revenue, 0);
    
    tbody.innerHTML = reportsData.products.topSelling.map((product, index) => {
        const percentage = ((product.revenue / totalRevenue) * 100).toFixed(1);
        return `
            <tr>
                <td><span class="rank">${index + 1}</span></td>
                <td>${product.name}</td>
                <td>${product.sales}</td>
                <td>${product.revenue.toLocaleString()} دج</td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${percentage}%"></div>
                        <span class="progress-text">${percentage}%</span>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Load recent activity
 */
function loadRecentActivity() {
    const activityList = document.getElementById('activityList');
    if (!activityList) return;
    
    const activities = [
        { type: 'order', message: 'طلب جديد #1234', time: 'منذ 5 دقائق', icon: 'shopping-cart' },
        { type: 'product', message: 'تم إضافة منتج جديد', time: 'منذ 15 دقيقة', icon: 'plus' },
        { type: 'customer', message: 'عميل جديد انضم', time: 'منذ 30 دقيقة', icon: 'user-plus' },
        { type: 'payment', message: 'تم استلام دفعة', time: 'منذ ساعة', icon: 'credit-card' },
        { type: 'review', message: 'تقييم جديد للمنتج', time: 'منذ ساعتين', icon: 'star' }
    ];
    
    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.type}">
                <i class="fas fa-${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <p class="activity-message">${activity.message}</p>
                <span class="activity-time">${activity.time}</span>
            </div>
        </div>
    `).join('');
}

/**
 * Export top products
 */
function exportTopProducts() {
    const data = reportsData.products.topSelling.map(product => ({
        'اسم المنتج': product.name,
        'عدد المبيعات': product.sales,
        'الإيرادات': product.revenue
    }));
    
    const csv = convertToCSV(data);
    downloadCSV(csv, 'top-products.csv');
    
    showNotification('تم تصدير قائمة أفضل المنتجات بنجاح', 'success');
}

/**
 * Export report
 */
function exportReport(format) {
    showNotification(`جاري تصدير التقرير بصيغة ${format.toUpperCase()}...`, 'info');
    
    // Simulate export process
    setTimeout(() => {
        showNotification(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
    }, 2000);
}

/**
 * Refresh activity
 */
function refreshActivity() {
    showNotification('جاري تحديث النشاط الأخير...', 'info');
    
    setTimeout(() => {
        loadRecentActivity();
        showNotification('تم تحديث النشاط الأخير بنجاح', 'success');
    }, 1000);
}

/**
 * Convert data to CSV
 */
function convertToCSV(data) {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');
    
    return csvContent;
}

/**
 * Download CSV file
 */
function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * Show notification
 */
function showNotification(message, type) {
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            case 'info':
                notificationManager.showInfo(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        alert(message);
    }
}

// Make functions globally available
window.initializeReports = initializeReports;
window.exportTopProducts = exportTopProducts;
window.exportReport = exportReport;
window.refreshActivity = refreshActivity;
