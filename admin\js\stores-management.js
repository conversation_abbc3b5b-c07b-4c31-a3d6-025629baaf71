/**
 * Stores Management System
 * Handles all store management functionality in the admin panel
 */

// Global variables
let stores = [];
let filteredStores = [];
let selectedStores = [];
let currentPage = 1;
let itemsPerPage = 10;
let currentStoreId = null;

// Store status configurations
const STORE_STATUS = {
    active: { label: 'نشط', class: 'success', icon: 'fas fa-check-circle' },
    blocked: { label: 'محظور', class: 'danger', icon: 'fas fa-ban' },
    pending: { label: 'قيد المراجعة', class: 'warning', icon: 'fas fa-clock' },
    suspended: { label: 'معلق', class: 'secondary', icon: 'fas fa-pause-circle' }
};

/**
 * Initialize stores management
 */
function initializeStoresManagement() {
    console.log('🏪 Initializing stores management...');

    // Add a small delay to ensure DOM is fully loaded
    setTimeout(() => {
        loadStores();
        setupEventListeners();
        console.log('✅ Stores management initialization complete');
    }, 100);
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('storeSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterStores, 300));
    }

    // Filter selects
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', filterStores);
    }

    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', sortStores);
    }
}

/**
 * Load stores from API
 */
async function loadStores() {
    try {
        console.log('🔄 Starting to load stores...');
        showLoading('جاري تحميل المتاجر...');

        // Add a small delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('📡 Fetching stores from API...');
        const response = await fetch('../php/api/stores.php');

        console.log('📊 API Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📋 Stores API response:', data);

        if (data.success) {
            stores = data.stores || [];
            filteredStores = [...stores];
            console.log('✅ Loaded stores successfully:', stores.length);

            // Clear any loading messages first
            hideLoading();

            // Wait a bit more to ensure table is ready
            await new Promise(resolve => setTimeout(resolve, 200));

            displayStores();
            updateStatistics();

            if (stores.length === 0) {
                showEmptyState();
            }
        } else {
            console.error('❌ Stores API error:', data);
            hideLoading();
            showError('فشل في تحميل المتاجر: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ Error loading stores:', error);

        // Check if it's a network error or API error
        if (error.message.includes('Failed to fetch')) {
            showError('خطأ في الاتصال بالخادم. تأكد من تشغيل الخادم وقاعدة البيانات.');
        } else if (error.message.includes('Unexpected token')) {
            showError('خطأ في تحليل البيانات. قد تكون هناك مشكلة في قاعدة البيانات.');
        } else {
            showError('خطأ في الاتصال بالخادم: ' + error.message);
        }

        // Load sample data as fallback only if it's not a critical error
        if (!error.message.includes('Failed to fetch')) {
            console.log('🔄 Loading sample data as fallback...');
            loadSampleStores();
        }
    } finally {
        hideLoading();
    }
}

/**
 * Show empty state when no stores are found
 */
function showEmptyState() {
    const tbody = document.getElementById('storesTableBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                    <div>
                        <i class="fas fa-store" style="font-size: 3rem; color: #ddd; margin-bottom: 15px;"></i>
                        <h4 style="margin: 0 0 10px 0; color: #999;">لا توجد متاجر حالياً</h4>
                        <p style="margin: 0 0 20px 0;">لم يتم إنشاء أي متاجر بعد</p>
                        <button class="btn btn-primary" onclick="createSampleStores()" style="background: #667eea; border: none; padding: 10px 20px; border-radius: 5px; color: white;">
                            <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}

/**
 * Create sample stores via API call
 */
async function createSampleStores() {
    try {
        showLoading('جاري إنشاء بيانات تجريبية...');

        const response = await fetch('../create-sample-stores.php');
        const text = await response.text();

        if (response.ok) {
            showSuccess('تم إنشاء البيانات التجريبية بنجاح');
            // Reload stores
            setTimeout(() => loadStores(), 1000);
        } else {
            showError('فشل في إنشاء البيانات التجريبية');
        }
    } catch (error) {
        showError('خطأ في إنشاء البيانات التجريبية: ' + error.message);
    }
}

/**
 * Load sample stores for testing
 */
function loadSampleStores() {
    console.log('Loading sample stores...');

    stores = [
        {
            id: 1,
            user_id: 1,
            store_name: 'متجر الكتب الإلكترونية',
            store_slug: 'ebooks-store',
            description: 'متجر متخصص في بيع الكتب الإلكترونية والمواد التعليمية',
            logo_url: null,
            status: 'active',
            domain: null,
            theme: 'modern',
            total_products: 25,
            total_orders: 150,
            total_revenue: 45000.00,
            created_at: '2024-01-15 10:30:00',
            updated_at: '2024-01-20 14:20:00',
            owner_name: 'أحمد محمد',
            owner_email: '<EMAIL>'
        },
        {
            id: 2,
            user_id: 2,
            store_name: 'متجر الإلكترونيات',
            store_slug: 'electronics-shop',
            description: 'متجر للأجهزة الإلكترونية والهواتف الذكية',
            logo_url: null,
            status: 'active',
            domain: null,
            theme: 'tech',
            total_products: 40,
            total_orders: 89,
            total_revenue: 125000.00,
            created_at: '2024-01-10 09:15:00',
            updated_at: '2024-01-18 16:45:00',
            owner_name: 'فاطمة علي',
            owner_email: '<EMAIL>'
        },
        {
            id: 3,
            user_id: 3,
            store_name: 'متجر الأزياء',
            store_slug: 'fashion-store',
            description: 'متجر للملابس والإكسسوارات العصرية',
            logo_url: null,
            status: 'pending',
            domain: null,
            theme: 'fashion',
            total_products: 60,
            total_orders: 45,
            total_revenue: 32000.00,
            created_at: '2024-01-20 11:00:00',
            updated_at: '2024-01-20 11:00:00',
            owner_name: 'محمد حسن',
            owner_email: '<EMAIL>'
        }
    ];

    filteredStores = [...stores];
    displayStores();
    updateStatistics();

    showSuccess('تم تحميل البيانات التجريبية');
}

/**
 * Display stores in table
 */
function displayStores() {
    const tbody = document.getElementById('storesTableBody');
    if (!tbody) {
        console.error('❌ storesTableBody element not found!');
        return;
    }

    console.log('📋 Displaying stores:', filteredStores.length);

    // Calculate pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageStores = filteredStores.slice(startIndex, endIndex);

    tbody.innerHTML = pageStores.map(store => `
        <tr class="store-row" data-store-id="${store.id}">
            <td>
                <input type="checkbox" class="store-checkbox" value="${store.id}"
                       onchange="toggleStoreSelection(${store.id}, this.checked)">
            </td>
            <td>
                <div class="store-info">
                    <div class="store-name">${store.store_name}</div>
                    <div class="store-slug">/${store.store_slug}</div>
                    ${store.domain ? `<div class="store-domain"><i class="fas fa-globe"></i> ${store.domain}</div>` : ''}
                </div>
            </td>
            <td>
                <div class="owner-info">
                    <div class="owner-name">${store.owner_name || 'غير محدد'}</div>
                    <div class="owner-email">${store.owner_email || ''}</div>
                    <div class="owner-id">ID: ${store.user_id}</div>
                </div>
            </td>
            <td>
                <span class="badge badge-${STORE_STATUS[store.status]?.class || 'secondary'}">
                    <i class="${STORE_STATUS[store.status]?.icon || 'fas fa-question'}"></i>
                    ${STORE_STATUS[store.status]?.label || store.status}
                </span>
            </td>
            <td>
                <div class="stat-value">${store.total_products || 0}</div>
            </td>
            <td>
                <div class="stat-value">${store.total_orders || 0}</div>
            </td>
            <td>
                <div class="revenue-value">${formatCurrency(store.total_revenue || 0)}</div>
            </td>
            <td>
                <div class="date-info">
                    <div class="created-date">${formatDate(store.created_at)}</div>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="viewStoreDetails(${store.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="changeStoreStatus(${store.id})" title="تغيير الحالة">
                        <i class="fas fa-toggle-on"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="accessAsOwner(${store.id})" title="الدخول كمالك">
                        <i class="fas fa-user-secret"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteStore(${store.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    updatePagination();
}

/**
 * Update statistics
 */
function updateStatistics() {
    const totalStores = stores.length;
    const activeStores = stores.filter(s => s.status === 'active').length;
    const blockedStores = stores.filter(s => s.status === 'blocked').length;
    const totalRevenue = stores.reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0);

    document.getElementById('totalStores').textContent = totalStores;
    document.getElementById('activeStores').textContent = activeStores;
    document.getElementById('blockedStores').textContent = blockedStores;
    document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);
}

/**
 * Filter stores based on search and filters
 */
function filterStores() {
    const searchTerm = document.getElementById('storeSearch').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;

    filteredStores = stores.filter(store => {
        const matchesSearch = !searchTerm ||
            store.store_name.toLowerCase().includes(searchTerm) ||
            store.store_slug.toLowerCase().includes(searchTerm) ||
            (store.owner_name && store.owner_name.toLowerCase().includes(searchTerm)) ||
            (store.owner_email && store.owner_email.toLowerCase().includes(searchTerm));

        const matchesStatus = !statusFilter || store.status === statusFilter;

        return matchesSearch && matchesStatus;
    });

    currentPage = 1;
    displayStores();
}

/**
 * Sort stores
 */
function sortStores() {
    const sortBy = document.getElementById('sortBy').value;

    filteredStores.sort((a, b) => {
        switch (sortBy) {
            case 'store_name':
                return a.store_name.localeCompare(b.store_name);
            case 'total_revenue':
                return (b.total_revenue || 0) - (a.total_revenue || 0);
            case 'total_orders':
                return (b.total_orders || 0) - (a.total_orders || 0);
            case 'created_at':
            default:
                return new Date(b.created_at) - new Date(a.created_at);
        }
    });

    displayStores();
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-DZ');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// UI feedback functions
function showLoading(message = 'جاري التحميل...') {
    console.log('Loading:', message);

    // Find the stores container
    const container = document.getElementById('storesContainer') ||
                     document.querySelector('.stores-container') ||
                     document.querySelector('.stores-management-content');

    if (container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 60px 20px; color: #666;">
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2.5rem; color: #667eea;"></i>
                </div>
                <h4 style="margin: 0 0 10px 0; color: #333;">${message}</h4>
                <p style="margin: 0; opacity: 0.7;">يرجى الانتظار...</p>
            </div>
        `;
    }
}

function hideLoading() {
    console.log('Loading finished');

    // Find and clear loading messages from all possible containers
    const containers = [
        document.getElementById('storesTableBody'),
        document.getElementById('storesContainer'),
        document.querySelector('.stores-container'),
        document.querySelector('.table-responsive')
    ];

    containers.forEach(container => {
        if (container && container.innerHTML.includes('جاري تحميل')) {
            console.log('Clearing loading message from container');
            // Don't clear completely, just remove loading if it's the only content
            if (container.innerHTML.includes('loading-spinner') && !container.innerHTML.includes('<tr')) {
                container.innerHTML = '';
            }
        }
    });
}

function showSuccess(message) {
    console.log('Success:', message);

    // Show success notification
    showNotification(message, 'success');
}

function showError(message) {
    console.log('Error:', message);

    // Find the stores container
    const container = document.getElementById('storesContainer') ||
                     document.querySelector('.stores-container') ||
                     document.querySelector('.stores-management-content');

    if (container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; color: #dc3545;"></i>
                </div>
                <h4 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل البيانات</h4>
                <p style="margin: 0 0 20px 0; color: #666;">${message}</p>
                <button class="btn btn-primary" onclick="loadStores()" style="background: #667eea; border: none; padding: 10px 20px; border-radius: 5px; color: white;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }

    // Also show notification
    showNotification(message, 'error');
}

function showNotification(message, type = 'info') {
    // Add CSS for animations if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
        font-family: 'Noto Sans Arabic', sans-serif;
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; margin-left: 10px; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Toggle store selection
 */
function toggleStoreSelection(storeId, isSelected) {
    if (isSelected) {
        if (!selectedStores.includes(storeId)) {
            selectedStores.push(storeId);
        }
    } else {
        selectedStores = selectedStores.filter(id => id !== storeId);
    }

    updateBulkActions();
}

/**
 * Toggle select all stores
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllStores');
    const storeCheckboxes = document.querySelectorAll('.store-checkbox');

    if (selectAllCheckbox.checked) {
        selectedStores = filteredStores.map(store => store.id);
        storeCheckboxes.forEach(checkbox => checkbox.checked = true);
    } else {
        selectedStores = [];
        storeCheckboxes.forEach(checkbox => checkbox.checked = false);
    }

    updateBulkActions();
}

/**
 * Update bulk actions visibility
 */
function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedStores.length > 0) {
        bulkActions.style.display = 'flex';
        selectedCount.textContent = selectedStores.length;
    } else {
        bulkActions.style.display = 'none';
    }
}

/**
 * View store details
 */
function viewStoreDetails(storeId) {
    const store = stores.find(s => s.id === storeId);
    if (!store) return;

    currentStoreId = storeId;

    const modal = document.getElementById('storeDetailsModal');
    const title = document.getElementById('storeDetailsTitle');
    const content = document.getElementById('storeDetailsContent');

    title.textContent = `تفاصيل متجر: ${store.store_name}`;

    content.innerHTML = `
        <div class="store-details">
            <div class="store-header">
                <div class="store-logo">
                    ${store.logo_url ?
                        `<img src="${store.logo_url}" alt="${store.store_name}">` :
                        `<div class="default-logo"><i class="fas fa-store"></i></div>`
                    }
                </div>
                <div class="store-basic-info">
                    <h4>${store.store_name}</h4>
                    <p class="store-slug">/${store.store_slug}</p>
                    <span class="badge badge-${STORE_STATUS[store.status]?.class}">
                        <i class="${STORE_STATUS[store.status]?.icon}"></i>
                        ${STORE_STATUS[store.status]?.label}
                    </span>
                </div>
            </div>

            <div class="store-info-grid">
                <div class="info-section">
                    <h5><i class="fas fa-user"></i> معلومات المالك</h5>
                    <div class="info-item">
                        <label>الاسم:</label>
                        <span>${store.owner_name || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد الإلكتروني:</label>
                        <span>${store.owner_email || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>معرف المستخدم:</label>
                        <span>#${store.user_id}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h5><i class="fas fa-chart-bar"></i> الإحصائيات</h5>
                    <div class="info-item">
                        <label>عدد المنتجات:</label>
                        <span>${store.total_products || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>عدد الطلبات:</label>
                        <span>${store.total_orders || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>إجمالي الإيرادات:</label>
                        <span>${formatCurrency(store.total_revenue || 0)}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h5><i class="fas fa-cog"></i> إعدادات المتجر</h5>
                    <div class="info-item">
                        <label>القالب:</label>
                        <span>${store.theme || 'افتراضي'}</span>
                    </div>
                    <div class="info-item">
                        <label>النطاق المخصص:</label>
                        <span>${store.domain || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>تاريخ الإنشاء:</label>
                        <span>${formatDate(store.created_at)}</span>
                    </div>
                </div>

                <div class="info-section full-width">
                    <h5><i class="fas fa-align-right"></i> الوصف</h5>
                    <p>${store.description || 'لا يوجد وصف'}</p>
                </div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

/**
 * Close store details modal
 */
function closeStoreDetailsModal() {
    const modal = document.getElementById('storeDetailsModal');
    modal.classList.remove('show');
    currentStoreId = null;
}

/**
 * Change store status
 */
function changeStoreStatus(storeId) {
    const store = stores.find(s => s.id === storeId);
    if (!store) return;

    currentStoreId = storeId;

    const modal = document.getElementById('statusChangeModal');
    const newStatusSelect = document.getElementById('newStatus');

    newStatusSelect.value = store.status;
    modal.classList.add('show');
}

/**
 * Close status change modal
 */
function closeStatusChangeModal() {
    const modal = document.getElementById('statusChangeModal');
    modal.classList.remove('show');
    currentStoreId = null;

    // Reset form
    document.getElementById('newStatus').value = '';
    document.getElementById('statusReason').value = '';
}

/**
 * Confirm status change
 */
async function confirmStatusChange() {
    if (!currentStoreId) return;

    const newStatus = document.getElementById('newStatus').value;
    const reason = document.getElementById('statusReason').value;

    try {
        showLoading('جاري تحديث حالة المتجر...');

        // Update store status in local data (for demo)
        const storeIndex = stores.findIndex(s => s.id === currentStoreId);
        if (storeIndex !== -1) {
            stores[storeIndex].status = newStatus;
            stores[storeIndex].updated_at = new Date().toISOString();
        }

        // Update filtered stores
        const filteredIndex = filteredStores.findIndex(s => s.id === currentStoreId);
        if (filteredIndex !== -1) {
            filteredStores[filteredIndex].status = newStatus;
        }

        showSuccess('تم تحديث حالة المتجر بنجاح');
        closeStatusChangeModal();
        displayStores();
        updateStatistics();

    } catch (error) {
        console.error('Error updating store status:', error);
        showError('خطأ في تحديث حالة المتجر');
    } finally {
        hideLoading();
    }
}

/**
 * Access store as owner (impersonation)
 */
function accessAsOwner(storeId) {
    const store = stores.find(s => s.id === storeId);
    if (!store) return;

    if (confirm(`هل تريد الدخول إلى متجر "${store.store_name}" كمالك؟\nسيتم تسجيل هذا الإجراء لأغراض الأمان.`)) {
        // In a real implementation, this would create a secure session
        // and redirect to the store's admin panel
        showSuccess(`تم الدخول إلى متجر "${store.store_name}" بنجاح`);

        // For demo, just show an alert
        alert(`🏪 مرحباً بك في متجر "${store.store_name}"\n\nهذه ميزة تجريبية. في التطبيق الحقيقي، ستتم إعادة توجيهك إلى لوحة تحكم المتجر.`);
    }
}

/**
 * Delete store
 */
function deleteStore(storeId) {
    const store = stores.find(s => s.id === storeId);
    if (!store) return;

    if (confirm(`هل أنت متأكد من حذف متجر "${store.store_name}"؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بالمتجر.`)) {
        try {
            showLoading('جاري حذف المتجر...');

            // Remove from stores array
            stores = stores.filter(s => s.id !== storeId);
            filteredStores = filteredStores.filter(s => s.id !== storeId);

            showSuccess('تم حذف المتجر بنجاح');
            displayStores();
            updateStatistics();

        } catch (error) {
            console.error('Error deleting store:', error);
            showError('خطأ في حذف المتجر');
        } finally {
            hideLoading();
        }
    }
}

/**
 * Bulk actions
 */
function bulkAction(action) {
    if (selectedStores.length === 0) return;

    let confirmMessage = '';
    switch (action) {
        case 'activate':
            confirmMessage = `هل تريد تفعيل ${selectedStores.length} متجر؟`;
            break;
        case 'block':
            confirmMessage = `هل تريد حظر ${selectedStores.length} متجر؟`;
            break;
        case 'delete':
            confirmMessage = `هل تريد حذف ${selectedStores.length} متجر؟\nتحذير: هذا الإجراء لا يمكن التراجع عنه.`;
            break;
    }

    if (confirm(confirmMessage)) {
        try {
            showLoading(`جاري تنفيذ الإجراء على ${selectedStores.length} متجر...`);

            selectedStores.forEach(storeId => {
                const storeIndex = stores.findIndex(s => s.id === storeId);
                if (storeIndex !== -1) {
                    switch (action) {
                        case 'activate':
                            stores[storeIndex].status = 'active';
                            break;
                        case 'block':
                            stores[storeIndex].status = 'blocked';
                            break;
                        case 'delete':
                            stores.splice(storeIndex, 1);
                            break;
                    }
                }
            });

            if (action === 'delete') {
                filteredStores = filteredStores.filter(s => !selectedStores.includes(s.id));
            }

            selectedStores = [];
            document.getElementById('selectAllStores').checked = false;

            showSuccess('تم تنفيذ الإجراء بنجاح');
            displayStores();
            updateStatistics();
            updateBulkActions();

        } catch (error) {
            console.error('Error performing bulk action:', error);
            showError('خطأ في تنفيذ الإجراء');
        } finally {
            hideLoading();
        }
    }
}

/**
 * Update pagination
 */
function updatePagination() {
    const totalPages = Math.ceil(filteredStores.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredStores.length);

    document.getElementById('paginationInfo').textContent =
        `عرض ${startItem}-${endItem} من ${filteredStores.length} متجر`;

    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('prevPage').disabled = currentPage === 1;
    document.getElementById('nextPage').disabled = currentPage === totalPages;
}

/**
 * Change page
 */
function changePage(direction) {
    const totalPages = Math.ceil(filteredStores.length / itemsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayStores();
    }
}

/**
 * Refresh stores
 */
function refreshStores() {
    loadStores();
}

/**
 * Export stores data
 */
function exportStores() {
    const csvContent = generateCSV(filteredStores);
    downloadCSV(csvContent, 'stores-export.csv');
    showSuccess('تم تصدير البيانات بنجاح');
}

/**
 * Generate CSV content
 */
function generateCSV(data) {
    const headers = ['ID', 'اسم المتجر', 'المالك', 'البريد الإلكتروني', 'الحالة', 'المنتجات', 'الطلبات', 'الإيرادات', 'تاريخ الإنشاء'];
    const rows = data.map(store => [
        store.id,
        store.store_name,
        store.owner_name || '',
        store.owner_email || '',
        STORE_STATUS[store.status]?.label || store.status,
        store.total_products || 0,
        store.total_orders || 0,
        store.total_revenue || 0,
        formatDate(store.created_at)
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

/**
 * Download CSV file
 */
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Make functions globally available
window.initializeStoresManagement = initializeStoresManagement;
window.loadStores = loadStores;
window.filterStores = filterStores;
window.sortStores = sortStores;
window.toggleStoreSelection = toggleStoreSelection;
window.toggleSelectAll = toggleSelectAll;
window.viewStoreDetails = viewStoreDetails;
window.closeStoreDetailsModal = closeStoreDetailsModal;
window.changeStoreStatus = changeStoreStatus;
window.closeStatusChangeModal = closeStatusChangeModal;
window.confirmStatusChange = confirmStatusChange;
window.accessAsOwner = accessAsOwner;
window.deleteStore = deleteStore;
window.bulkAction = bulkAction;
window.changePage = changePage;
window.refreshStores = refreshStores;
window.exportStores = exportStores;

console.log('🏪 Stores management script loaded');
