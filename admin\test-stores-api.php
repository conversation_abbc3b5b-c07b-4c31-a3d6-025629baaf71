<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المتاجر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ef4444;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #3b82f6;
        }
        .output {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a67d8;
        }
        .button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API المتاجر</h1>
        
        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <ul>
                <li><strong>فحص قاعدة البيانات</strong> - التحقق من وجود جدول المتاجر</li>
                <li><strong>اختبار API المتاجر</strong> - اختبار استجابة API</li>
                <li><strong>إنشاء بيانات تجريبية</strong> - إضافة متاجر للاختبار</li>
            </ul>
        </div>

        <button class="button" onclick="checkDatabase()" id="checkDbBtn">🔍 فحص قاعدة البيانات</button>
        <button class="button" onclick="testStoresAPI()" id="testApiBtn">🧪 اختبار API المتاجر</button>
        <button class="button" onclick="createSampleData()" id="sampleBtn">🌱 إنشاء بيانات تجريبية</button>
        <button class="button" onclick="runMigration()" id="migrationBtn">🚀 تشغيل الترحيل</button>

        <div id="output"></div>
    </div>

    <script>
        async function checkDatabase() {
            const outputDiv = document.getElementById('output');
            const checkDbBtn = document.getElementById('checkDbBtn');
            
            checkDbBtn.disabled = true;
            checkDbBtn.textContent = '⏳ جاري الفحص...';
            
            outputDiv.innerHTML = '<div class="info">🔍 فحص قاعدة البيانات...</div>';
            
            try {
                const response = await fetch('test-database-check.php');
                const text = await response.text();
                
                if (response.ok) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ تم فحص قاعدة البيانات بنجاح!</div>
                        <div class="output">${text}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ فشل في فحص قاعدة البيانات</div>
                        <div class="output">${text}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في الاتصال: ${error.message}</div>
                `;
            }
            
            checkDbBtn.disabled = false;
            checkDbBtn.textContent = '🔍 فحص قاعدة البيانات';
        }

        async function testStoresAPI() {
            const outputDiv = document.getElementById('output');
            const testApiBtn = document.getElementById('testApiBtn');
            
            testApiBtn.disabled = true;
            testApiBtn.textContent = '⏳ جاري الاختبار...';
            
            outputDiv.innerHTML = '<div class="info">🧪 اختبار API المتاجر...</div>';
            
            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();
                
                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ API المتاجر يعمل بشكل صحيح!</div>
                        <div class="info">📊 عدد المتاجر: ${data.total || 0}</div>
                        <div class="output">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ مشكلة في API المتاجر: ${data.message}</div>
                        <div class="output">${JSON.stringify(data, null, 2)}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في اختبار API: ${error.message}</div>
                    <div class="info">💡 قد تحتاج إلى تشغيل الترحيل أولاً</div>
                `;
            }
            
            testApiBtn.disabled = false;
            testApiBtn.textContent = '🧪 اختبار API المتاجر';
        }

        async function createSampleData() {
            const outputDiv = document.getElementById('output');
            const sampleBtn = document.getElementById('sampleBtn');
            
            sampleBtn.disabled = true;
            sampleBtn.textContent = '⏳ جاري الإنشاء...';
            
            outputDiv.innerHTML = '<div class="info">🌱 إنشاء بيانات تجريبية...</div>';
            
            try {
                const response = await fetch('create-sample-stores.php');
                const text = await response.text();
                
                if (response.ok) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ تم إنشاء البيانات التجريبية بنجاح!</div>
                        <div class="output">${text}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ فشل في إنشاء البيانات التجريبية</div>
                        <div class="output">${text}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في إنشاء البيانات: ${error.message}</div>
                `;
            }
            
            sampleBtn.disabled = false;
            sampleBtn.textContent = '🌱 إنشاء بيانات تجريبية';
        }

        async function runMigration() {
            const outputDiv = document.getElementById('output');
            const migrationBtn = document.getElementById('migrationBtn');
            
            migrationBtn.disabled = true;
            migrationBtn.textContent = '⏳ جاري التشغيل...';
            
            outputDiv.innerHTML = '<div class="info">🚀 تشغيل ترحيل قاعدة البيانات...</div>';
            
            try {
                const response = await fetch('../php/migrations/create_stores_table.php');
                const text = await response.text();
                
                if (response.ok) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ تم تشغيل الترحيل بنجاح!</div>
                        <div class="output">${text}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ فشل في تشغيل الترحيل</div>
                        <div class="output">${text}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في تشغيل الترحيل: ${error.message}</div>
                `;
            }
            
            migrationBtn.disabled = false;
            migrationBtn.textContent = '🚀 تشغيل الترحيل';
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(checkDatabase, 1000);
        });
    </script>
</body>
</html>
