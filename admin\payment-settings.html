<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الدفع - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Payment Settings Content -->
    <div class="payment-settings-content">
        <!-- Header Section -->
        <div class="payment-settings-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="section-title-content">
                    <h3 class="section-title">إعدادات الدفع</h3>
                    <p class="section-subtitle">تكوين وإدارة طرق الدفع والمعاملات المالية</p>
                </div>
            </div>
            <div class="settings-summary">
                <div class="summary-item">
                    <span class="summary-label">طرق الدفع المفعلة:</span>
                    <span class="summary-value" id="activePaymentMethods">--</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">حالة النظام:</span>
                    <span class="summary-value status-active">متصل</span>
                </div>
            </div>
        </div>

        <!-- Payment Methods Configuration -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-money-check-alt"></i>
                    طرق الدفع
                </h4>
                <p class="section-description">تكوين طرق الدفع المختلفة المتاحة للعملاء</p>
            </div>

            <div class="settings-grid">
                <!-- Credit Card Settings -->
                <div class="setting-card">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="setting-info">
                            <h5>البطاقات الائتمانية</h5>
                            <p>Visa, MasterCard, American Express</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="creditCardEnabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-group">
                            <label for="stripePublicKey" class="enhanced-label">
                                <i class="fas fa-key"></i>
                                Stripe Public Key
                            </label>
                            <input type="text" id="stripePublicKey" class="enhanced-input" placeholder="pk_test_...">
                        </div>
                        <div class="form-group">
                            <label for="stripeSecretKey" class="enhanced-label">
                                <i class="fas fa-lock"></i>
                                Stripe Secret Key
                            </label>
                            <div class="input-group">
                                <input type="password" id="stripeSecretKey" class="enhanced-input" placeholder="sk_test_...">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('stripeSecretKey')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PayPal Settings -->
                <div class="setting-card">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="setting-info">
                            <h5>PayPal</h5>
                            <p>دفع آمن عبر PayPal</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="paypalEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-group">
                            <label for="paypalClientId" class="enhanced-label">
                                <i class="fas fa-id-card"></i>
                                PayPal Client ID
                            </label>
                            <input type="text" id="paypalClientId" class="enhanced-input" placeholder="Client ID">
                        </div>
                        <div class="form-group">
                            <label for="paypalClientSecret" class="enhanced-label">
                                <i class="fas fa-lock"></i>
                                PayPal Client Secret
                            </label>
                            <div class="input-group">
                                <input type="password" id="paypalClientSecret" class="enhanced-input" placeholder="Client Secret">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('paypalClientSecret')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Transfer Settings -->
                <div class="setting-card">
                    <div class="setting-header">
                        <div class="setting-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="setting-info">
                            <h5>التحويل البنكي</h5>
                            <p>دفع عبر التحويل البنكي المباشر</p>
                        </div>
                        <div class="setting-toggle">
                            <label class="switch">
                                <input type="checkbox" id="bankTransferEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-content">
                        <div class="form-group">
                            <label for="bankName" class="enhanced-label">
                                <i class="fas fa-building"></i>
                                اسم البنك
                            </label>
                            <input type="text" id="bankName" class="enhanced-input" placeholder="اسم البنك">
                        </div>
                        <div class="form-group">
                            <label for="accountNumber" class="enhanced-label">
                                <i class="fas fa-hashtag"></i>
                                رقم الحساب
                            </label>
                            <input type="text" id="accountNumber" class="enhanced-input" placeholder="رقم الحساب">
                        </div>
                        <div class="form-group">
                            <label for="iban" class="enhanced-label">
                                <i class="fas fa-code"></i>
                                IBAN
                            </label>
                            <input type="text" id="iban" class="enhanced-input" placeholder="IBAN">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency Settings -->
        <div class="settings-section">
            <div class="section-header">
                <h4 class="section-title">
                    <i class="fas fa-coins"></i>
                    إعدادات العملة
                </h4>
                <p class="section-description">تكوين العملة الافتراضية وأسعار الصرف</p>
            </div>

            <div class="settings-grid">
                <div class="setting-card">
                    <div class="form-group">
                        <label for="defaultCurrency" class="enhanced-label">
                            <i class="fas fa-money-bill"></i>
                            العملة الافتراضية
                        </label>
                        <select id="defaultCurrency" class="enhanced-select">
                            <option value="DZD">دينار جزائري (DZD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="SAR">ريال سعودي (SAR)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currencySymbol" class="enhanced-label">
                            <i class="fas fa-at"></i>
                            رمز العملة
                        </label>
                        <input type="text" id="currencySymbol" class="enhanced-input" value="د.ج" placeholder="د.ج">
                    </div>
                    <div class="form-group">
                        <label for="currencyPosition" class="enhanced-label">
                            <i class="fas fa-align-left"></i>
                            موضع رمز العملة
                        </label>
                        <select id="currencyPosition" class="enhanced-select">
                            <option value="before">قبل المبلغ</option>
                            <option value="after" selected>بعد المبلغ</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Actions -->
        <div class="save-actions">
            <button type="button" class="enhanced-save-btn" onclick="savePaymentSettings()">
                <i class="fas fa-save"></i>
                حفظ إعدادات الدفع
            </button>
            <button type="button" class="enhanced-test-btn" onclick="testPaymentConnection()">
                <i class="fas fa-vial"></i>
                اختبار الاتصال
            </button>
        </div>
    </div>

    <script src="js/payment-settings.js"></script>
</body>
</html>
