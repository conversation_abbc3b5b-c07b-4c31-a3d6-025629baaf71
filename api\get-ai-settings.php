<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/config.php';
require_once '../php/AIManager.php';

try {
    $aiManager = AIManager::getInstance();
    $providers = ['openai', 'anthropic', 'gemini'];
    $settings = [];

    foreach ($providers as $provider) {
        $config = $aiManager->getProviderConfig($provider);

        if ($config) {
            $settings[$provider] = [
                'enabled' => $config['enabled'],
                'api_key' => $config['api_key'], // This will include .env fallback
                'model' => getDefaultModel($provider),
                'status' => $config['status_message'],
                'last_tested' => $config['last_tested']
            ];
        } else {
            // Fallback to default values
            $settings[$provider] = [
                'enabled' => false,
                'api_key' => '',
                'model' => getDefaultModel($provider),
                'status' => 'Not configured',
                'last_tested' => null
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
} catch (Exception $e) {
    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getDefaultModel($provider)
{
    switch ($provider) {
        case 'openai':
            return 'gpt-3.5-turbo';
        case 'anthropic':
            return 'claude-3-sonnet';
        case 'gemini':
            return 'gemini-pro';
        default:
            return 'default';
    }
}
