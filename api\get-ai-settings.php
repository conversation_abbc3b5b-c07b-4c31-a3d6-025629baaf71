<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/bootstrap.php';
require_once '../php/AIManager.php';

try {
    $aiManager = AIManager::getInstance();
    $providers = ['openai', 'anthropic', 'gemini'];
    $settings = [];

    foreach ($providers as $provider) {
        $config = $aiManager->getProviderConfig($provider);

        if ($config) {
            $settings[$provider] = [
                'enabled' => (bool)$config['enabled'],
                'api_key' => $config['api_key'] ?: '', // This will include .env fallback
                'model' => $aiManager->getDefaultModel($provider),
                'status' => $config['status_message'] ?: 'Ready',
                'last_tested' => $config['last_tested']
            ];
        } else {
            // No configuration found at all
            $settings[$provider] = [
                'enabled' => false,
                'api_key' => '',
                'model' => $aiManager->getDefaultModel($provider),
                'status' => 'Not configured',
                'last_tested' => null
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
} catch (Exception $e) {
    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
