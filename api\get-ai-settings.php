<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/config.php';
require_once '../config/security.php';
require_once '../config/ai.php';

try {
    Security::init();
    Security::validateRequest();

    $aiConfig = AIManager::getInstance();
    $providers = ['openai', 'anthropic', 'gemini'];
    $settings = [];

    foreach ($providers as $provider) {
        $status = $aiConfig->getProviderStatus($provider);
        $settings[$provider] = [
            'enabled' => $status['enabled'],
            'status' => $status['status'],
            'message' => $status['message'],
            'model' => $aiConfig->isProviderEnabled($provider) ?
                Config::get(strtoupper($provider) . '_MODEL', 'default') : null
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $settings,
        'csrf_token' => Security::generateCSRFToken()
    ]);
} catch (Exception $e) {
    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
