<?php
/**
 * Roles Management API
 * Handles CRUD operations for user roles and permissions
 */

require_once '../../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Start session for authentication
session_start();

try {
    $db = Database::getInstance();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'list' || empty($action)) {
                handleGetRoles($db);
            } elseif ($action === 'permissions') {
                handleGetPermissions($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'POST':
            if ($action === 'create') {
                handleCreateRole($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'PUT':
            if ($action === 'update') {
                handleUpdateRole($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'DELETE':
            if ($action === 'delete') {
                handleDeleteRole($db);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        default:
            throw new Exception('Method not allowed');
    }

} catch (Exception $e) {
    error_log("Roles API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get all roles with user counts
 */
function handleGetRoles($db) {
    try {
        $stmt = $db->prepare("
            SELECT 
                ur.*,
                COUNT(u.id) as user_count
            FROM user_roles ur
            LEFT JOIN users u ON ur.id = u.role_id
            GROUP BY ur.id
            ORDER BY ur.level DESC
        ");
        
        $stmt->execute();
        $roles = $stmt->fetchAll();

        // Format roles data
        $formattedRoles = array_map(function($role) {
            return [
                'id' => (int)$role['id'],
                'name' => $role['name'],
                'display_name_ar' => $role['display_name_ar'],
                'display_name_en' => $role['display_name_en'],
                'description' => $role['description'],
                'permissions' => json_decode($role['permissions'] ?? '[]', true),
                'level' => (int)$role['level'],
                'is_active' => (bool)$role['is_active'],
                'user_count' => (int)$role['user_count'],
                'created_at' => $role['created_at'],
                'updated_at' => $role['updated_at']
            ];
        }, $roles);

        echo json_encode([
            'success' => true,
            'roles' => $formattedRoles,
            'total' => count($formattedRoles)
        ]);

    } catch (Exception $e) {
        throw new Exception('Failed to fetch roles: ' . $e->getMessage());
    }
}

/**
 * Get available permissions
 */
function handleGetPermissions($db) {
    try {
        $permissions = [
            'system' => [
                'name' => 'إدارة النظام',
                'permissions' => [
                    'system.admin' => 'الوصول لإدارة النظام',
                    'system.settings' => 'تعديل إعدادات النظام',
                    'system.backup' => 'النسخ الاحتياطي',
                    'system.logs' => 'عرض سجلات النظام'
                ]
            ],
            'users' => [
                'name' => 'إدارة المستخدمين',
                'permissions' => [
                    'users.view' => 'عرض المستخدمين',
                    'users.create' => 'إنشاء مستخدمين',
                    'users.edit' => 'تعديل المستخدمين',
                    'users.delete' => 'حذف المستخدمين',
                    'users.roles' => 'إدارة الأدوار'
                ]
            ],
            'products' => [
                'name' => 'إدارة المنتجات',
                'permissions' => [
                    'products.view' => 'عرض المنتجات',
                    'products.create' => 'إنشاء منتجات',
                    'products.edit' => 'تعديل المنتجات',
                    'products.delete' => 'حذف المنتجات',
                    'products.categories' => 'إدارة الفئات'
                ]
            ],
            'orders' => [
                'name' => 'إدارة الطلبات',
                'permissions' => [
                    'orders.view' => 'عرض الطلبات',
                    'orders.edit' => 'تعديل الطلبات',
                    'orders.delete' => 'حذف الطلبات',
                    'orders.export' => 'تصدير الطلبات'
                ]
            ],
            'landing_pages' => [
                'name' => 'صفحات الهبوط',
                'permissions' => [
                    'landing_pages.view' => 'عرض صفحات الهبوط',
                    'landing_pages.create' => 'إنشاء صفحات الهبوط',
                    'landing_pages.edit' => 'تعديل صفحات الهبوط',
                    'landing_pages.delete' => 'حذف صفحات الهبوط'
                ]
            ],
            'store' => [
                'name' => 'إدارة المتجر',
                'permissions' => [
                    'store.settings' => 'إعدادات المتجر',
                    'store.payment' => 'إعدادات الدفع',
                    'store.shipping' => 'إعدادات الشحن',
                    'store.analytics' => 'تحليلات المتجر'
                ]
            ]
        ];

        echo json_encode([
            'success' => true,
            'permissions' => $permissions
        ]);

    } catch (Exception $e) {
        throw new Exception('Failed to fetch permissions: ' . $e->getMessage());
    }
}

/**
 * Create a new role
 */
function handleCreateRole($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid input data');
        }

        // Validate required fields
        $required = ['name', 'display_name_ar', 'display_name_en', 'level'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Check if role name already exists
        $stmt = $db->prepare("SELECT id FROM user_roles WHERE name = ?");
        $stmt->execute([$input['name']]);
        if ($stmt->fetch()) {
            throw new Exception('Role name already exists');
        }

        // Insert role
        $stmt = $db->prepare("
            INSERT INTO user_roles (name, display_name_ar, display_name_en, description, permissions, level, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $permissions = json_encode($input['permissions'] ?? []);

        $stmt->execute([
            $input['name'],
            $input['display_name_ar'],
            $input['display_name_en'],
            $input['description'] ?? '',
            $permissions,
            $input['level'],
            $input['is_active'] ?? 1
        ]);

        $roleId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Role created successfully',
            'role_id' => $roleId
        ]);

    } catch (Exception $e) {
        throw new Exception('Failed to create role: ' . $e->getMessage());
    }
}

/**
 * Update an existing role
 */
function handleUpdateRole($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || empty($input['id'])) {
            throw new Exception('Invalid input data or missing role ID');
        }

        $roleId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = ['name', 'display_name_ar', 'display_name_en', 'description', 'level', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        // Handle permissions separately
        if (isset($input['permissions'])) {
            $updates[] = "permissions = ?";
            $params[] = json_encode($input['permissions']);
        }

        if (empty($updates)) {
            throw new Exception('No fields to update');
        }

        $params[] = $roleId;

        $sql = "UPDATE user_roles SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'Role updated successfully'
        ]);

    } catch (Exception $e) {
        throw new Exception('Failed to update role: ' . $e->getMessage());
    }
}

/**
 * Delete a role
 */
function handleDeleteRole($db) {
    try {
        $roleId = $_GET['id'] ?? '';
        
        if (empty($roleId)) {
            throw new Exception('Role ID is required');
        }

        // Check if role is in use
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role_id = ?");
        $stmt->execute([$roleId]);
        $userCount = $stmt->fetch()['count'];

        if ($userCount > 0) {
            throw new Exception("Cannot delete role: $userCount users are assigned to this role");
        }

        // Check if it's a system role (level >= 80)
        $stmt = $db->prepare("SELECT level FROM user_roles WHERE id = ?");
        $stmt->execute([$roleId]);
        $role = $stmt->fetch();

        if (!$role) {
            throw new Exception('Role not found');
        }

        if ($role['level'] >= 80) {
            throw new Exception('Cannot delete system roles');
        }

        $stmt = $db->prepare("DELETE FROM user_roles WHERE id = ?");
        $stmt->execute([$roleId]);

        echo json_encode([
            'success' => true,
            'message' => 'Role deleted successfully'
        ]);

    } catch (Exception $e) {
        throw new Exception('Failed to delete role: ' . $e->getMessage());
    }
}
?>
