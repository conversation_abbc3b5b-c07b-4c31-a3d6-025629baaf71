/**
 * AI Magic Wand System for Mossaab Landing Page Admin
 * Provides automatic text generation with Arabic RTL support
 */

class AIMagicWand {
    constructor() {
        this.isAvailable = false;
        this.providers = [];
        this.currentProvider = null;
        this.init();
    }

    async init() {
        await this.checkAvailability();
        this.createStyles();
        this.setupEventListeners();

        // Wait for TinyMCE to be ready before adding magic wands
        this.waitForTinyMCE();

        console.log('🪄 AI Magic Wand initialized');
    }

    /**
     * Wait for TinyMCE to be ready and add magic wands to existing editors
     */
    waitForTinyMCE() {
        if (typeof tinymce !== 'undefined' && tinymce.editors && Array.isArray(tinymce.editors)) {
            // TinyMCE is ready, add magic wands to existing editors
            tinymce.editors.forEach(editor => {
                this.addMagicWandToTinyMCEEditor(editor);
            });
        } else {
            // TinyMCE not ready yet, wait and try again
            setTimeout(() => this.waitForTinyMCE(), 500);
        }
    }

    /**
     * Check if AI is available
     */
    async checkAvailability() {
        try {
            const response = await safeApiCall('/php/api/ai.php?action=get_config');
            if (response.success) {
                this.isAvailable = response.data.is_available;
                this.providers = response.data.available_providers;
                this.currentProvider = response.data.config.default_provider;
                console.log('🤖 AI Available:', this.isAvailable, 'Providers:', this.providers);
            }
        } catch (error) {
            console.warn('AI not available:', error);
            this.isAvailable = false;
        }
    }

    /**
     * Create magic wand styles
     */
    createStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .magic-wand-btn {
                display: inline-flex;
                align-items: center;
                gap: 5px;
                padding: 6px 12px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-right: 8px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
            }

            .magic-wand-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }

            .magic-wand-btn:active {
                transform: translateY(0);
            }

            .magic-wand-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }

            .magic-wand-btn .icon {
                font-size: 14px;
                animation: sparkle 2s infinite;
            }

            @keyframes sparkle {
                0%, 100% { transform: rotate(0deg) scale(1); }
                25% { transform: rotate(-5deg) scale(1.1); }
                75% { transform: rotate(5deg) scale(1.1); }
            }

            .magic-wand-loading {
                display: inline-flex;
                align-items: center;
                gap: 5px;
                color: #667eea;
                font-size: 12px;
                direction: rtl;
            }

            .magic-wand-loading .spinner {
                width: 12px;
                height: 12px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .ai-status-indicator {
                display: inline-flex;
                align-items: center;
                gap: 5px;
                padding: 4px 8px;
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 4px;
                font-size: 11px;
                color: #0369a1;
                margin-right: 8px;
                direction: rtl;
            }

            .ai-status-indicator.unavailable {
                background: #fef2f2;
                border-color: #ef4444;
                color: #dc2626;
            }

            .ai-error-message {
                background: #fef2f2;
                border: 1px solid #fecaca;
                color: #dc2626;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                margin-top: 8px;
                direction: rtl;
            }

            .ai-success-message {
                background: #f0fdf4;
                border: 1px solid #bbf7d0;
                color: #166534;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                margin-top: 8px;
                direction: rtl;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for dynamic content changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.addMagicWandsToElement(node);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Add magic wands to existing elements
        this.addMagicWandsToPage();
    }

    /**
     * Add magic wands to the entire page
     */
    addMagicWandsToPage() {
        // Product description fields
        this.addMagicWandToField('productDescription', 'product_description', 'إنشاء وصف تلقائي');

        // Landing page title fields
        this.addMagicWandToField('pageTitle', 'landing_page_title', 'إنشاء عنوان تلقائي');

        // Landing page content fields
        this.addMagicWandToField('pageContent', 'landing_page_content', 'إنشاء محتوى تلقائي');

        // Meta description fields
        this.addMagicWandToField('metaDescription', 'meta_description', 'إنشاء وصف ميتا تلقائي');

        // Add to TinyMCE editors
        this.addMagicWandsToTinyMCE();
    }

    /**
     * Add magic wands to specific element
     */
    addMagicWandsToElement(element) {
        const textareas = element.querySelectorAll('textarea');
        const inputs = element.querySelectorAll('input[type="text"]');

        [...textareas, ...inputs].forEach(field => {
            if (!field.dataset.magicWandAdded) {
                this.addMagicWandToSpecificField(field);
            }
        });
    }

    /**
     * Add magic wand to specific field
     */
    addMagicWandToField(fieldId, type, label) {
        const field = document.getElementById(fieldId);
        if (field && !field.dataset.magicWandAdded) {
            this.addMagicWandToSpecificField(field, type, label);
        }
    }

    /**
     * Add magic wand to specific field element
     */
    addMagicWandToSpecificField(field, type = null, label = null) {
        if (field.dataset.magicWandAdded) return;

        // Determine type from field attributes
        if (!type) {
            if (field.id.includes('description') || field.name.includes('description')) {
                type = 'product_description';
                label = 'إنشاء وصف تلقائي';
            } else if (field.id.includes('title') || field.name.includes('title')) {
                type = 'landing_page_title';
                label = 'إنشاء عنوان تلقائي';
            } else if (field.id.includes('content') || field.name.includes('content')) {
                type = 'landing_page_content';
                label = 'إنشاء محتوى تلقائي';
            } else if (field.id.includes('meta') || field.name.includes('meta')) {
                type = 'meta_description';
                label = 'إنشاء وصف ميتا تلقائي';
            } else {
                type = 'product_description';
                label = 'إنشاء نص تلقائي';
            }
        }

        const container = this.createMagicWandContainer(field, type, label);
        field.parentNode.insertBefore(container, field.nextSibling);
        field.dataset.magicWandAdded = 'true';
    }

    /**
     * Create magic wand container
     */
    createMagicWandContainer(field, type, label) {
        const container = document.createElement('div');
        container.className = 'magic-wand-container';
        container.style.marginTop = '8px';

        if (this.isAvailable) {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'magic-wand-btn';
            button.innerHTML = `<span class="icon">🪄</span> ${label}`;

            button.addEventListener('click', () => {
                this.generateText(field, type);
            });

            container.appendChild(button);

            // Add status indicator
            const status = document.createElement('span');
            status.className = 'ai-status-indicator';
            status.innerHTML = `<span>🤖</span> متاح (${this.providers.join(', ')})`;
            container.appendChild(status);
        } else {
            const status = document.createElement('span');
            status.className = 'ai-status-indicator unavailable';
            status.innerHTML = `<span>⚠️</span> الذكاء الاصطناعي غير متاح`;
            container.appendChild(status);
        }

        return container;
    }

    /**
     * Add magic wands to TinyMCE editors
     */
    addMagicWandsToTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            tinymce.on('AddEditor', (e) => {
                const editor = e.editor;
                editor.on('init', () => {
                    this.addMagicWandToTinyMCEEditor(editor);
                });
            });

            // Existing editors are handled by waitForTinyMCE() method
        }
    }

    /**
     * Add magic wand to TinyMCE editor
     */
    addMagicWandToTinyMCEEditor(editor) {
        if (!this.isAvailable) return;

        editor.ui.registry.addButton('magicwand', {
            text: '🪄 إنشاء تلقائي',
            onAction: () => {
                const textarea = document.getElementById(editor.id);
                const type = this.determineTinyMCEType(editor.id);
                this.generateText(textarea, type, editor);
            }
        });

        // Add to toolbar if not already there
        const toolbar = editor.settings.toolbar;
        if (toolbar && !toolbar.includes('magicwand')) {
            editor.settings.toolbar = toolbar + ' | magicwand';
        }
    }

    /**
     * Determine TinyMCE editor type
     */
    determineTinyMCEType(editorId) {
        if (editorId.includes('description')) return 'product_description';
        if (editorId.includes('content')) return 'landing_page_content';
        if (editorId.includes('title')) return 'landing_page_title';
        return 'product_description';
    }

    /**
     * Generate text using AI
     */
    async generateText(field, type, editor = null) {
        if (!this.isAvailable) {
            this.showError(field, 'الذكاء الاصطناعي غير متاح');
            return;
        }

        const button = field.parentNode.querySelector('.magic-wand-btn');
        const originalText = button.innerHTML;

        // Show loading state
        button.disabled = true;
        button.innerHTML = `<div class="spinner"></div> جاري الإنشاء...`;

        try {
            // Collect product data from form
            const productData = this.collectProductData();

            // Make API call
            const response = await safeApiCall('/php/api/ai.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: `generate_${type}`,
                    product: productData
                })
            });

            if (response.success) {
                const generatedText = response.data[type.replace('generate_', '')] ||
                                   response.data.description ||
                                   response.data.title ||
                                   response.data.content ||
                                   response.data.meta_description;

                // Set the generated text
                if (editor) {
                    editor.setContent(generatedText);
                } else {
                    field.value = generatedText;
                    // Trigger change event
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                }

                this.showSuccess(field, `تم إنشاء النص بواسطة ${response.data.provider}`);
            } else {
                this.showError(field, response.message || 'فشل في إنشاء النص');
            }

        } catch (error) {
            console.error('AI generation error:', error);
            this.showError(field, 'خطأ في الاتصال بالذكاء الاصطناعي');
        } finally {
            // Restore button
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    /**
     * Collect product data from current form
     */
    collectProductData() {
        const data = {};

        // Common field mappings
        const fieldMappings = {
            'productTitle': 'title',
            'productName': 'title',
            'title': 'title',
            'productDescription': 'description',
            'description': 'description',
            'productPrice': 'price',
            'price': 'price',
            'productCategory': 'category',
            'category': 'category'
        };

        // Collect data from form fields
        Object.entries(fieldMappings).forEach(([fieldId, dataKey]) => {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                data[dataKey] = field.value;
            }
        });

        // Try to get data from selected product (for landing pages)
        const productSelect = document.getElementById('productSelect');
        if (productSelect && productSelect.value) {
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.product) {
                try {
                    const productData = JSON.parse(selectedOption.dataset.product);
                    Object.assign(data, productData);
                } catch (e) {
                    console.warn('Could not parse product data:', e);
                }
            }
        }

        return data;
    }

    /**
     * Show success message
     */
    showSuccess(field, message) {
        this.removeMessages(field);
        const successDiv = document.createElement('div');
        successDiv.className = 'ai-success-message';
        successDiv.textContent = message;
        field.parentNode.insertBefore(successDiv, field.nextSibling);

        setTimeout(() => successDiv.remove(), 5000);
    }

    /**
     * Show error message
     */
    showError(field, message) {
        this.removeMessages(field);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ai-error-message';
        errorDiv.textContent = message;
        field.parentNode.insertBefore(errorDiv, field.nextSibling);

        setTimeout(() => errorDiv.remove(), 5000);
    }

    /**
     * Remove existing messages
     */
    removeMessages(field) {
        const container = field.parentNode;
        const messages = container.querySelectorAll('.ai-success-message, .ai-error-message');
        messages.forEach(msg => msg.remove());
    }

    /**
     * Refresh AI availability
     */
    async refresh() {
        await this.checkAvailability();
        // Update existing UI elements
        document.querySelectorAll('.ai-status-indicator').forEach(indicator => {
            if (this.isAvailable) {
                indicator.className = 'ai-status-indicator';
                indicator.innerHTML = `<span>🤖</span> متاح (${this.providers.join(', ')})`;
            } else {
                indicator.className = 'ai-status-indicator unavailable';
                indicator.innerHTML = `<span>⚠️</span> الذكاء الاصطناعي غير متاح`;
            }
        });
    }
}

// Initialize AI Magic Wand when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.aiMagicWand = new AIMagicWand();
});

// Make it available globally
window.AIMagicWand = AIMagicWand;
