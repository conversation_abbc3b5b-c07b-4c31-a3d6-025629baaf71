/* Critical CSS Fixes for Mossaab Landing Page Admin Panel */

/* 1. FOUC Prevention - Critical styles loaded first */
:root {
    /* RTL and Direction Properties */
    writing-mode: horizontal-tb;
    direction: rtl;
    text-orientation: mixed;
    
    /* Critical Layout Variables */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --border-radius-lg: 16px;
    --spacing-lg: 30px;
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --color-text-primary: #1e293b;
    --color-text-secondary: #64748b;
    --color-border: #e2e8f0;
    --color-background: #f8fafc;
}

/* 2. Immediate Body Styles to Prevent FOUC */
body {
    font-family: 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
    background-color: #f8fafc !important;
    color: #1e293b !important;
    line-height: 1.6 !important;
    direction: rtl !important;
    margin: 0 !important;
    padding: 0 !important;
    /* Prevent flash of unstyled content */
    visibility: visible !important;
    opacity: 1 !important;
}

/* 3. Fix Missing H1 Styles in Sections */
section h1,
.content-section h1,
.admin-section h1,
.section-title,
.page-title {
    font-size: 2rem !important;
    font-weight: 600 !important;
    margin: 0 0 1.5rem 0 !important;
    color: var(--color-text-primary) !important;
    line-height: 1.2 !important;
    font-family: 'Noto Sans Arabic', sans-serif !important;
}

/* 4. Fix Invisible UI Elements (width: 0, height: 0) */
.btn,
.button,
.action-button,
.enhanced-btn,
button:not(.close):not(.modal-close) {
    min-width: 120px !important;
    min-height: 40px !important;
    width: auto !important;
    height: auto !important;
    padding: 10px 20px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 5. Fix Section Display Issues */
.content-section,
.admin-section,
section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    min-height: auto !important;
}

/* 6. Landing Page Specific Button Fixes */
.landing-page-btn,
.add-landing-page-btn,
#addLandingPageBtn,
.landing-pages-action-btn {
    min-width: 150px !important;
    min-height: 44px !important;
    width: auto !important;
    height: auto !important;
    padding: 12px 24px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    background: var(--primary-gradient) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: var(--transition-base) !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.landing-page-btn:hover,
.add-landing-page-btn:hover,
#addLandingPageBtn:hover,
.landing-pages-action-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* 7. Modal and Form Element Fixes */
.modal,
.modal-content,
.modal-dialog {
    visibility: visible !important;
    opacity: 1 !important;
    width: auto !important;
    height: auto !important;
    min-width: 300px !important;
    min-height: 200px !important;
}

.form-control,
.form-input,
input,
textarea,
select {
    min-width: 200px !important;
    min-height: 40px !important;
    width: 100% !important;
    height: auto !important;
    padding: 10px 15px !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 8. Navigation and Sidebar Fixes */
.sidebar,
.admin-nav,
.navigation {
    visibility: visible !important;
    opacity: 1 !important;
    width: auto !important;
    min-width: 250px !important;
    height: auto !important;
    min-height: 100vh !important;
}

.nav-item,
.nav-link,
.sidebar-item {
    min-width: 200px !important;
    min-height: 44px !important;
    width: auto !important;
    height: auto !important;
    padding: 12px 20px !important;
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 9. Table and List Fixes */
.table,
.data-table,
table {
    width: 100% !important;
    min-width: 600px !important;
    height: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.table td,
.table th,
.data-table td,
.data-table th,
table td,
table th {
    min-width: 80px !important;
    min-height: 40px !important;
    width: auto !important;
    height: auto !important;
    padding: 12px 15px !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 10. Card and Container Fixes */
.card,
.settings-card,
.stat-card,
.admin-card {
    min-width: 250px !important;
    min-height: 150px !important;
    width: auto !important;
    height: auto !important;
    padding: 20px !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* 11. Icon and Image Fixes */
.icon,
.fa,
.fas,
.far,
i[class*="fa"] {
    min-width: 16px !important;
    min-height: 16px !important;
    width: auto !important;
    height: auto !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.image-preview,
.product-image,
.landing-page-image {
    min-width: 60px !important;
    min-height: 60px !important;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 12. Responsive Fixes */
@media (max-width: 768px) {
    .btn,
    .button,
    .action-button,
    .enhanced-btn {
        min-width: 100px !important;
        min-height: 44px !important;
        padding: 12px 16px !important;
    }
    
    section h1,
    .content-section h1,
    .admin-section h1,
    .section-title,
    .page-title {
        font-size: 1.5rem !important;
        margin: 0 0 1rem 0 !important;
    }
    
    .sidebar,
    .admin-nav {
        min-width: 280px !important;
    }
}

/* 13. Critical Loading States */
.loading,
.spinner,
.loader {
    min-width: 40px !important;
    min-height: 40px !important;
    width: 40px !important;
    height: 40px !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
}

/* 14. Notification Fixes */
.notification,
.alert,
.message {
    min-width: 250px !important;
    min-height: 50px !important;
    width: auto !important;
    height: auto !important;
    padding: 15px 20px !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* 15. Force Visibility for Hidden Elements */
[style*="display: none"],
[style*="visibility: hidden"],
[style*="opacity: 0"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Exception for intentionally hidden elements */
.modal:not(.show),
.dropdown-menu:not(.show),
.collapse:not(.show),
.hidden,
.d-none {
    display: none !important;
}

/* 16. Critical Typography */
* {
    font-family: 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 17. Ensure RTL Support */
html {
    direction: rtl !important;
}

html[dir="rtl"] *,
[dir="rtl"] * {
    direction: rtl !important;
    text-align: right !important;
}

/* 18. Critical Animation Fixes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out !important;
}

/* 19. Print Styles */
@media print {
    * {
        visibility: visible !important;
        opacity: 1 !important;
        width: auto !important;
        height: auto !important;
    }
}
