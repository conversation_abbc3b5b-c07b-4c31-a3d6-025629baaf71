<?php

/**
 * AI Manager - Centralized AI Configuration and Provider Management
 * Handles AI provider configuration, validation, and status management
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/Security.php';

class AIManager
{
    private static $instance = null;
    private $pdo = null;
    private $config;
    private $security;

    private function __construct()
    {
        // Config is a static class, not singleton
        $this->config = new class {
            public function get($key, $default = null)
            {
                return Config::get($key, $default);
            }

            public function getDatabaseConfig()
            {
                return Config::getDbConfig();
            }
        };

        $this->security = Security::getInstance();
        $this->initializeDatabase();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function initializeDatabase()
    {
        if ($this->pdo === null) {
            try {
                $database = Database::getInstance();
                $this->pdo = $database->getPDO();
            } catch (Exception $e) {
                error_log("AIManager database initialization failed: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
    }

    // Get provider configuration and status
    public function getProviderConfig($provider)
    {
        // First try to get from database
        $stmt = $this->pdo->prepare("
            SELECT provider, enabled, api_key, status_message, last_tested
            FROM ai_settings
            WHERE provider = :provider
        ");
        $stmt->execute(['provider' => $provider]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            // Decrypt API key
            if (!empty($result['api_key'])) {
                $result['api_key'] = $this->security->decrypt($result['api_key']);
            }
            return $result;
        }

        // Fallback to .env file if no database entry exists
        $envKey = strtoupper($provider) . '_API_KEY';
        $apiKey = $this->config->get($envKey);

        if (!empty($apiKey)) {
            return [
                'provider' => $provider,
                'enabled' => !empty($apiKey), // Enable if key exists
                'api_key' => $apiKey,
                'status_message' => 'Loaded from environment configuration',
                'last_tested' => null
            ];
        }

        return null;
    }

    // Get default model for provider
    public function getDefaultModel($provider)
    {
        $models = [
            'openai' => 'gpt-3.5-turbo',
            'anthropic' => 'claude-3-sonnet-20240229',
            'gemini' => 'gemini-pro'
        ];

        return $models[$provider] ?? 'default';
    }

    // Update provider configuration
    public function updateProviderConfig($provider, $apiKey, $enabled = true)
    {
        try {
            // Validate API key format
            $this->security->validateApiKey($apiKey, $provider);

            // Test connection
            $success = $this->security->testProviderConnection($provider, $apiKey);
            $statusMessage = $success ? 'Connection successful' : 'Connection failed';

            // Encrypt API key for storage
            $encryptedKey = $this->security->encrypt($apiKey);

            $stmt = $this->pdo->prepare("
                INSERT INTO ai_settings
                    (provider, enabled, api_key, status_message, last_tested)
                VALUES
                    (:provider, :enabled, :api_key, :status, CURRENT_TIMESTAMP)
                ON DUPLICATE KEY UPDATE
                    enabled = :enabled,
                    api_key = :api_key,
                    status_message = :status,
                    last_tested = CURRENT_TIMESTAMP
            ");

            $stmt->execute([
                'provider' => $provider,
                'enabled' => $enabled && $success,
                'api_key' => $encryptedKey,
                'status' => $statusMessage
            ]);

            return [
                'success' => true,
                'message' => $statusMessage,
                'enabled' => $enabled && $success
            ];
        } catch (Exception $e) {
            // Log error and update status
            error_log("AIManager: Error updating {$provider} configuration: " . $e->getMessage());

            $stmt = $this->pdo->prepare("
                UPDATE ai_settings
                SET status_message = :status,
                    enabled = false,
                    last_tested = CURRENT_TIMESTAMP
                WHERE provider = :provider
            ");

            $stmt->execute([
                'status' => $e->getMessage(),
                'provider' => $provider
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'enabled' => false
            ];
        }
    }

    // Check if a provider is enabled and available
    public function isProviderAvailable($provider)
    {
        $config = $this->getProviderConfig($provider);
        return $config && $config['enabled'];
    }

    // Get all provider statuses
    public function getAllProviderStatuses()
    {
        $stmt = $this->pdo->query("
            SELECT provider, enabled, status_message, last_tested
            FROM ai_settings
            ORDER BY provider
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Test all provider connections
    public function testAllProviders()
    {
        $results = [];
        $stmt = $this->pdo->query("SELECT provider, api_key FROM ai_settings");

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $provider = $row['provider'];
            $apiKey = $this->security->decrypt($row['api_key']);

            try {
                $success = $this->security->testProviderConnection($provider, $apiKey);
                $statusMessage = $success ? 'Connection successful' : 'Connection failed';
                $enabled = $success;
            } catch (Exception $e) {
                $success = false;
                $statusMessage = $e->getMessage();
                $enabled = false;
            }

            // Update status in database
            $this->pdo->prepare("
                UPDATE ai_settings
                SET status_message = :status,
                    enabled = :enabled,
                    last_tested = CURRENT_TIMESTAMP
                WHERE provider = :provider
            ")->execute([
                'status' => $statusMessage,
                'enabled' => $enabled,
                'provider' => $provider
            ]);

            $results[$provider] = [
                'success' => $success,
                'message' => $statusMessage,
                'enabled' => $enabled
            ];
        }

        return $results;
    }

    // Get the preferred available provider
    public function getActiveProvider()
    {
        $preferredOrder = [
            $this->config->get('DEFAULT_AI_PROVIDER', 'openai'),
            'openai',
            'anthropic',
            'gemini'
        ];

        $stmt = $this->pdo->query("
            SELECT provider
            FROM ai_settings
            WHERE enabled = true
            ORDER BY FIELD(provider, 'openai', 'anthropic', 'gemini')
        ");

        $availableProviders = $stmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($preferredOrder as $provider) {
            if (in_array($provider, $availableProviders)) {
                return $provider;
            }
        }

        return null;
    }

    // Legacy compatibility methods
    public function getApiKey($provider)
    {
        $config = $this->getProviderConfig($provider);
        return $config ? $config['api_key'] : null;
    }

    public function setApiKey($provider, $apiKey)
    {
        return $this->updateProviderConfig($provider, $apiKey);
    }
}
