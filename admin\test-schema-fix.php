<?php
/**
 * Test Schema Fix
 * Quick test to verify the database schema fix for stores API
 */

require_once '../php/config.php';

echo "🧪 Testing Database Schema Fix...\n\n";

try {
    $pdo = getPDOConnection();
    
    // Test 1: Check users table structure
    echo "📋 Test 1: Checking users table structure...\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['first_name', 'last_name', 'email', 'phone'];
    $missingColumns = array_diff($requiredColumns, $columnNames);
    
    if (empty($missingColumns)) {
        echo "✅ Users table has correct schema\n";
        echo "   - first_name: " . (in_array('first_name', $columnNames) ? '✅' : '❌') . "\n";
        echo "   - last_name: " . (in_array('last_name', $columnNames) ? '✅' : '❌') . "\n";
        echo "   - email: " . (in_array('email', $columnNames) ? '✅' : '❌') . "\n";
        echo "   - phone: " . (in_array('phone', $columnNames) ? '✅' : '❌') . "\n";
    } else {
        echo "❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
    }
    
    // Test 2: Check if users exist
    echo "\n📋 Test 2: Checking users data...\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    echo "Users count: {$userCount}\n";
    
    if ($userCount == 0) {
        echo "⚠️ No users found. Creating sample users...\n";
        
        // Create a sample user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, first_name, last_name, email, phone, password, role_id, subscription_id, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'test_user',
            'مستخدم',
            'تجريبي',
            '<EMAIL>',
            '0555123456',
            password_hash('123456', PASSWORD_DEFAULT),
            4, // customer role
            1, // basic subscription
            'active'
        ]);
        echo "✅ Sample user created\n";
    }
    
    // Test 3: Test the fixed query
    echo "\n📋 Test 3: Testing fixed stores API query...\n";
    $sql = "
        SELECT
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone,
            u.created_at as user_created_at
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Query executed successfully!\n";
    echo "Found " . count($stores) . " stores\n";
    
    if (!empty($stores)) {
        echo "\nSample store data:\n";
        foreach ($stores as $index => $store) {
            echo "Store " . ($index + 1) . ":\n";
            echo "   Name: {$store['store_name']}\n";
            echo "   Owner: {$store['owner_name']}\n";
            echo "   Email: {$store['owner_email']}\n";
            echo "   Phone: {$store['owner_phone']}\n";
            echo "   Status: {$store['status']}\n\n";
        }
    }
    
    // Test 4: Test the actual API endpoint
    echo "📋 Test 4: Testing API endpoint...\n";
    
    // Use curl to test the API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ cURL error: {$error}\n";
    } else {
        echo "📊 HTTP Response Code: {$httpCode}\n";
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "✅ API returned valid JSON\n";
                echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
                echo "   Total stores: " . ($data['total'] ?? 'unknown') . "\n";
                echo "   Message: " . ($data['message'] ?? 'no message') . "\n";
                
                if ($data['success'] && $data['total'] > 0) {
                    echo "\n🎉 SCHEMA FIX SUCCESSFUL! API is working correctly.\n";
                } else {
                    echo "\n⚠️ API works but no stores found. You may need to create sample data.\n";
                }
            } else {
                echo "❌ API returned invalid JSON\n";
                echo "Response: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "❌ API returned HTTP {$httpCode}\n";
            echo "Response: " . substr($response, 0, 200) . "...\n";
        }
    }
    
    echo "\n📋 Summary:\n";
    echo "=" . str_repeat("=", 30) . "\n";
    echo "✅ Database schema: Fixed\n";
    echo "✅ Query syntax: Fixed\n";
    echo "✅ API endpoint: " . ($httpCode === 200 ? 'Working' : 'Needs attention') . "\n";
    
    if ($httpCode === 200) {
        echo "\n🎉 All tests passed! The store management system should now work.\n";
        echo "\n🔗 Next steps:\n";
        echo "   1. Visit: http://localhost:8000/admin/\n";
        echo "   2. Click: إدارة المتاجر\n";
        echo "   3. The interface should load within 3 seconds\n";
    } else {
        echo "\n⚠️ Some issues remain. Check the API response above.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
}
?>
