<?php

/**
 * Individual Store Page
 * Displays products for a specific store
 */

require_once 'php/config.php';

// Get store slug from URL parameter
$storeSlug = $_GET['store'] ?? '';

if (empty($storeSlug)) {
    header('Location: /');
    exit;
}

try {
    $pdo = getPDOConnection();

    // Get store information
    $stmt = $pdo->prepare("
        SELECT
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.store_slug = ? AND s.status = 'active'
    ");
    $stmt->execute([$storeSlug]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$store) {
        header('HTTP/1.0 404 Not Found');
        include 'error.html';
        exit;
    }

    // Get store products (products with store_id matching this store, or NULL store_id for demo)
    $stmt = $pdo->prepare("
        SELECT
            p.*,
            c.nom as category_name
        FROM produits p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE (p.store_id = ? OR p.store_id IS NULL) AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$store['id']]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Store settings
    $storeSettings = json_decode($store['settings'] ?? '{}', true);
    $currency = $storeSettings['currency'] ?? 'DZD';
    $themeColor = $storeSettings['theme_color'] ?? '#667eea';
} catch (Exception $e) {
    error_log("Store page error: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    include 'error.html';
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($store['store_name']); ?> - متجر إلكتروني</title>
    <meta name="description" content="<?php echo htmlspecialchars($store['description'] ?? ''); ?>">

    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: <?php echo $themeColor; ?>;
            --primary-dark: <?php echo adjustBrightness($themeColor, -20); ?>;
        }

        .store-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .store-info {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .store-name {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .store-description {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .store-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .products-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: #333;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .product-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .product-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .order-btn {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .order-btn:hover {
            background: var(--primary-dark);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .contact-section {
            background: white;
            padding: 40px 0;
            text-align: center;
        }

        .contact-info {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .contact-item {
            display: inline-block;
            margin: 0 20px;
            color: #666;
        }

        .contact-item i {
            color: var(--primary-color);
            margin-left: 8px;
        }
    </style>
</head>

<body>
    <!-- Store Header -->
    <header class="store-header">
        <div class="store-info">
            <h1 class="store-name"><?php echo htmlspecialchars($store['store_name']); ?></h1>
            <?php if (!empty($store['description'])): ?>
                <p class="store-description"><?php echo htmlspecialchars($store['description']); ?></p>
            <?php endif; ?>

            <div class="store-stats">
                <div class="stat-item">
                    <span class="stat-number"><?php echo count($products); ?></span>
                    <span class="stat-label">منتج</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $store['total_orders'] ?? 0; ?></span>
                    <span class="stat-label">طلب</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">⭐ 4.8</span>
                    <span class="stat-label">تقييم</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">منتجاتنا</h2>

            <?php if (!empty($products)): ?>
                <div class="products-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card">
                            <img src="<?php echo htmlspecialchars($product['image'] ?? 'images/default-product.jpg'); ?>"
                                alt="<?php echo htmlspecialchars($product['nom']); ?>"
                                class="product-image">

                            <div class="product-info">
                                <h3 class="product-name"><?php echo htmlspecialchars($product['nom']); ?></h3>
                                <div class="product-price">
                                    <?php echo number_format($product['prix'], 0, '.', ','); ?> <?php echo $currency; ?>
                                </div>
                                <?php if (!empty($product['description'])): ?>
                                    <p class="product-description">
                                        <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                        <?php if (strlen($product['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                <?php endif; ?>

                                <button class="order-btn" onclick="orderProduct(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-shopping-cart"></i>
                                    اطلب الآن
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-box-open"></i>
                    </div>
                    <h3>لا توجد منتجات حالياً</h3>
                    <p>سيتم إضافة منتجات جديدة قريباً</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="contact-info">
            <h3>تواصل معنا</h3>
            <div style="margin-top: 20px;">
                <?php if (!empty($store['owner_email'])): ?>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <?php echo htmlspecialchars($store['owner_email']); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($store['owner_phone'])): ?>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <?php echo htmlspecialchars($store['owner_phone']); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script>
        function orderProduct(productId) {
            // Redirect to order form with product and store information
            const storeSlug = '<?php echo $storeSlug; ?>';
            window.location.href = `order-form.html?product=${productId}&store=${storeSlug}`;
        }
    </script>
</body>

</html>

<?php
/**
 * Helper function to adjust color brightness
 */
function adjustBrightness($hex, $percent)
{
    // Remove # if present
    $hex = ltrim($hex, '#');

    // Convert to RGB
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    // Adjust brightness
    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));

    // Convert back to hex
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>
