/**
 * Categories Management JavaScript
 * Handles CRUD operations for product categories
 */

// Available icons for categories
const availableIcons = [
    'fas fa-box', 'fas fa-book', 'fas fa-laptop', 'fas fa-shopping-bag', 'fas fa-tshirt',
    'fas fa-home', 'fas fa-car', 'fas fa-mobile-alt', 'fas fa-gamepad', 'fas fa-music',
    'fas fa-camera', 'fas fa-bicycle', 'fas fa-utensils', 'fas fa-tools', 'fas fa-paint-brush',
    'fas fa-dumbbell', 'fas fa-baby', 'fas fa-paw', 'fas fa-seedling', 'fas fa-gift'
];

let categories = [];
let editingCategoryId = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🗂️ Categories management initialized');
    initializeIconSelector();
    loadCategories();
    setupEventListeners();
});

// Initialize icon selector
function initializeIconSelector() {
    const iconSelector = document.getElementById('iconSelector');

    if (!iconSelector) {
        console.warn('⚠️ IconSelector element not found. Categories management may not be on this page.');
        return;
    }

    iconSelector.innerHTML = '';

    availableIcons.forEach(iconClass => {
        const iconOption = document.createElement('div');
        iconOption.className = 'icon-option';
        iconOption.innerHTML = `<i class="${iconClass}"></i>`;
        iconOption.onclick = () => selectIcon(iconClass, iconOption);
        iconSelector.appendChild(iconOption);
    });

    // Select first icon by default
    if (iconSelector.firstChild) {
        selectIcon(availableIcons[0], iconSelector.firstChild);
    }
}

// Select an icon
function selectIcon(iconClass, element) {
    // Remove previous selection
    document.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('selected'));

    // Add selection to clicked element
    element.classList.add('selected');

    // Update hidden input
    document.getElementById('selectedIcon').value = iconClass;
}

// Setup event listeners
function setupEventListeners() {
    // Category form (used for both add and edit)
    const categoryForm = document.getElementById('categoryForm');
    if (categoryForm) {
        categoryForm.addEventListener('submit', handleCategorySubmit);
    }

    // Auto-generate slug from category name
    const categoryNameField = document.getElementById('categoryName');
    const categorySlugField = document.getElementById('categorySlug');
    if (categoryNameField && categorySlugField) {
        categoryNameField.addEventListener('input', function() {
            const arabicName = this.value;
            const slug = transliterateToEnglish(arabicName);
            categorySlugField.value = slug;
        });
    }

    // Category search functionality
    const categorySearch = document.getElementById('categorySearch');
    if (categorySearch) {
        categorySearch.addEventListener('input', function() {
            filterCategories(this.value);
        });
    }
}

// Simple transliteration (you can enhance this)
function transliterateToEnglish(arabicText) {
    const transliterationMap = {
        'كتب': 'books',
        'حاسوب': 'laptop',
        'حقائب': 'bags',
        'ملابس': 'clothing',
        'منزل': 'home',
        'أجهزة': 'devices',
        'رياضة': 'sports',
        'طعام': 'food',
        'سيارات': 'cars',
        'هواتف': 'phones'
    };

    // Check if we have a direct translation
    for (const [arabic, english] of Object.entries(transliterationMap)) {
        if (arabicText.includes(arabic)) {
            return english;
        }
    }

    // Fallback: convert to lowercase and remove spaces
    return arabicText.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
}

// Load categories from API
async function loadCategories() {
    try {
        showLoading('جاري تحميل الفئات...');

        const response = await fetch('../php/api/categories.php');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Categories API response:', data);

        if (data.success) {
            categories = data.categories || [];
            console.log('Loaded categories:', categories);
            displayCategories();
            updateStatistics();
        } else {
            console.error('Categories API error:', data);
            showError('فشل في تحميل الفئات: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        showError('خطأ في الاتصال بالخادم: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Display categories
function displayCategories() {
    const categoriesTree = document.getElementById('categoriesTree');

    if (!categoriesTree) {
        console.warn('⚠️ Categories tree container not found');
        return;
    }

    if (categories.length === 0) {
        categoriesTree.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-folder-open" style="font-size: 3em; margin-bottom: 15px;"></i>
                <p>لا توجد فئات حالياً. قم بإضافة فئة جديدة أعلاه.</p>
            </div>
        `;
        return;
    }

    categoriesTree.innerHTML = categories.map(category => `
        <div class="category-card" data-id="${category.id}">
            <div class="category-header">
                <div style="display: flex; align-items: center;">
                    <i class="${category.icone}" style="color: ${category.couleur}; font-size: 2em; margin-left: 15px;"></i>
                    <div class="category-info">
                        <h3>${category.nom_ar} (${category.nom_en})</h3>
                        <p>${category.description_ar || 'لا يوجد وصف'}</p>
                        <small style="color: #999;">المنتجات: ${category.products_count || 0}</small>
                    </div>
                </div>
                <div class="category-actions">
                    <button class="btn btn-toggle ${category.actif ? '' : 'inactive'}"
                            onclick="toggleCategory(${category.id}, ${category.actif})">
                        <i class="fas fa-${category.actif ? 'eye' : 'eye-slash'}"></i>
                        ${category.actif ? 'نشط' : 'معطل'}
                    </button>
                    <button class="btn btn-edit" onclick="showEditCategoryModal(${category.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-delete" onclick="deleteCategory(${category.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Update statistics
function updateStatistics() {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.actif).length;

    const totalCategoriesEl = document.getElementById('totalCategories');
    const activeCategoriesEl = document.getElementById('activeCategories');
    const lastUpdatedEl = document.getElementById('lastUpdated');

    if (totalCategoriesEl) totalCategoriesEl.textContent = totalCategories;
    if (activeCategoriesEl) activeCategoriesEl.textContent = activeCategories;
    if (lastUpdatedEl) lastUpdatedEl.textContent = new Date().toLocaleString('ar-DZ');
}

// Handle add category
async function handleAddCategory(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const categoryData = Object.fromEntries(formData.entries());

    try {
        showLoading('جاري إضافة الفئة...');

        const response = await fetch('../php/api/categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(categoryData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إضافة الفئة بنجاح');
            event.target.reset();
            initializeIconSelector(); // Reset icon selection
            loadCategories(); // Reload categories
        } else {
            showError('فشل في إضافة الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error adding category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Edit category
function editCategory(categoryId) {
    const category = categories.find(cat => cat.id == categoryId);
    if (!category) return;

    editingCategoryId = categoryId;

    // Populate edit form
    document.getElementById('editCategoryId').value = categoryId;
    document.getElementById('editNomAr').value = category.nom_ar;
    document.getElementById('editNomEn').value = category.nom_en;
    document.getElementById('editDescAr').value = category.description_ar || '';
    document.getElementById('editDescEn').value = category.description_en || '';
    document.getElementById('editCouleur').value = category.couleur;
    document.getElementById('editOrdre').value = category.ordre_affichage;

    // Show modal
    document.getElementById('editCategoryModal').style.display = 'flex';
}

// Handle edit category
async function handleEditCategory(event) {
    event.preventDefault();

    const categoryId = document.getElementById('editCategoryId').value;
    const formData = new FormData(event.target);
    const categoryData = Object.fromEntries(formData.entries());
    categoryData.id = categoryId;

    try {
        showLoading('جاري تحديث الفئة...');

        const response = await fetch('../php/api/categories.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(categoryData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تحديث الفئة بنجاح');
            closeEditModal();
            loadCategories(); // Reload categories
        } else {
            showError('فشل في تحديث الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error updating category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Toggle category status
async function toggleCategory(categoryId, currentStatus) {
    try {
        showLoading('جاري تحديث حالة الفئة...');

        const response = await fetch('../php/api/categories.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: categoryId,
                actif: currentStatus ? 0 : 1
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تحديث حالة الفئة بنجاح');
            loadCategories(); // Reload categories
        } else {
            showError('فشل في تحديث حالة الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error toggling category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Delete category
async function deleteCategory(categoryId) {
    const category = categories.find(cat => cat.id == categoryId);
    if (!category) return;

    if (!confirm(`هل أنت متأكد من حذف فئة "${category.nom_ar}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    try {
        showLoading('جاري حذف الفئة...');

        const response = await fetch(`../php/api/categories.php?id=${categoryId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف الفئة بنجاح');
            loadCategories(); // Reload categories
        } else {
            showError('فشل في حذف الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Close edit modal
function closeEditModal() {
    document.getElementById('editCategoryModal').style.display = 'none';
    editingCategoryId = null;
}

// Utility functions for notifications
function showLoading(message) {
    // You can implement a loading spinner here
    console.log('Loading:', message);
}

function hideLoading() {
    // Hide loading spinner
    console.log('Loading finished');
}

function showSuccess(message) {
    alert('✅ ' + message);
}

function showError(message) {
    alert('❌ ' + message);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const categoryModal = document.getElementById('categoryModal');
    if (categoryModal && event.target === categoryModal) {
        closeCategoryModal();
    }

    // Also handle the old edit modal if it exists
    const editModal = document.getElementById('editCategoryModal');
    if (editModal && event.target === editModal) {
        closeEditModal();
    }
});

// Initialize categories management
function initializeCategoriesManagement() {
    console.log('🗂️ Initializing categories management...');

    // Add a small delay to ensure DOM is fully loaded
    setTimeout(() => {
        // Check if required elements exist
        const categoryModal = document.getElementById('categoryModal');
        if (!categoryModal) {
            console.warn('⚠️ Category modal not found during initialization');
        }

        initializeIconSelector();
        loadCategories();
        setupEventListeners();

        console.log('✅ Categories management initialization complete');
    }, 100);
}

// Handle category form submission (for both add and edit)
function handleCategorySubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const categoryData = {
        nom_ar: document.getElementById('categoryName').value,
        slug: document.getElementById('categorySlug').value,
        parent_id: document.getElementById('parentCategory').value || null,
        description_ar: document.getElementById('categoryDescription').value,
        actif: document.getElementById('categoryStatus').checked ? 1 : 0
    };

    if (editingCategoryId) {
        updateCategory(editingCategoryId, categoryData);
    } else {
        addCategory(categoryData);
    }
}

// Add new category
async function addCategory(categoryData) {
    try {
        showLoading('جاري إضافة الفئة...');

        const response = await fetch('../php/api/categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(categoryData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إضافة الفئة بنجاح');
            closeCategoryModal();
            loadCategories();
        } else {
            showError('فشل في إضافة الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error adding category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Update existing category
async function updateCategory(categoryId, categoryData) {
    try {
        showLoading('جاري تحديث الفئة...');

        const response = await fetch(`../php/api/categories.php?id=${categoryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(categoryData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تحديث الفئة بنجاح');
            closeCategoryModal();
            loadCategories();
        } else {
            showError('فشل في تحديث الفئة: ' + (data.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error updating category:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        hideLoading();
    }
}

// Show add category modal
function showAddCategoryModal() {
    editingCategoryId = null;

    // Check if modal elements exist
    const modalTitle = document.getElementById('modalTitle');
    const categoryForm = document.getElementById('categoryForm');
    const categoryModal = document.getElementById('categoryModal');

    if (!categoryModal) {
        console.error('Category modal not found');
        alert('خطأ: لم يتم العثور على نافذة إضافة الفئة');
        return;
    }

    // Update modal title
    if (modalTitle) {
        modalTitle.textContent = 'إضافة فئة جديدة';
    }

    // Reset form
    if (categoryForm) {
        categoryForm.reset();
    }

    // Show modal
    categoryModal.style.display = 'flex';
    categoryModal.classList.add('show');
}

// Show edit category modal
function showEditCategoryModal(categoryId) {
    editingCategoryId = categoryId;

    // Check if modal elements exist
    const modalTitle = document.getElementById('modalTitle');
    const categoryModal = document.getElementById('categoryModal');

    if (!categoryModal) {
        console.error('Category modal not found');
        alert('خطأ: لم يتم العثور على نافذة تعديل الفئة');
        return;
    }

    // Update modal title
    if (modalTitle) {
        modalTitle.textContent = 'تعديل الفئة';
    }

    // Find category data
    const category = categories.find(cat => cat.id == categoryId);
    if (category) {
        // Safely set form values with null checks
        const categoryName = document.getElementById('categoryName');
        if (categoryName) categoryName.value = category.nom_ar || '';

        const categorySlug = document.getElementById('categorySlug');
        if (categorySlug) categorySlug.value = category.slug || '';

        const parentCategory = document.getElementById('parentCategory');
        if (parentCategory) parentCategory.value = category.parent_id || '';

        const categoryDescription = document.getElementById('categoryDescription');
        if (categoryDescription) categoryDescription.value = category.description_ar || '';

        const categoryStatus = document.getElementById('categoryStatus');
        if (categoryStatus) categoryStatus.checked = category.actif == 1;
    }

    // Show modal
    categoryModal.style.display = 'flex';
    categoryModal.classList.add('show');
}

// Close category modal
function closeCategoryModal() {
    const modal = document.getElementById('categoryModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }
    editingCategoryId = null;
}

// Save category (called by modal button)
function saveCategory() {
    const form = document.getElementById('categoryForm');
    if (form) {
        form.dispatchEvent(new Event('submit'));
    } else {
        console.error('Category form not found');
    }
}

// Filter categories based on search
function filterCategories(searchTerm) {
    const categoriesTree = document.getElementById('categoriesTree');
    if (!categoriesTree) return;

    const categoryCards = categoriesTree.querySelectorAll('.category-card');

    categoryCards.forEach(card => {
        const categoryName = card.querySelector('h3')?.textContent.toLowerCase() || '';
        const categoryDesc = card.querySelector('p')?.textContent.toLowerCase() || '';
        const searchLower = searchTerm.toLowerCase();

        if (categoryName.includes(searchLower) || categoryDesc.includes(searchLower)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Export/Import functions
function exportCategories() {
    const dataStr = JSON.stringify(categories, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'categories_export.json';
    link.click();
}

function importCategories() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedCategories = JSON.parse(e.target.result);
                    // Process imported categories here
                    console.log('Imported categories:', importedCategories);
                    showSuccess('تم استيراد الفئات بنجاح');
                } catch (error) {
                    showError('خطأ في تنسيق الملف');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

// Expand/Collapse functions
function expandAllCategories() {
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.classList.add('expanded');
        const details = card.querySelector('.category-details');
        if (details) {
            details.style.display = 'block';
        }
    });
    console.log('Expanding all categories');
}

function collapseAllCategories() {
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.classList.remove('expanded');
        const details = card.querySelector('.category-details');
        if (details) {
            details.style.display = 'none';
        }
    });
    console.log('Collapsing all categories');
}

/**
 * Enhanced notification system
 */
function showNotification(message, type = 'info') {
    // Use the global notification system if available
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        // Fallback notification
        const alertType = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
        alert(alertType + ' ' + message);
    }
}

/**
 * Enhanced loading state management
 */
function showLoadingState() {
    const treeContainer = document.getElementById('categoriesTree');
    if (treeContainer) {
        treeContainer.innerHTML = `
            <div class="tree-loading">
                <i class="fas fa-spinner fa-spin"></i>
                جاري تحميل الفئات...
            </div>
        `;
    }
}

function showErrorState(message) {
    const treeContainer = document.getElementById('categoriesTree');
    if (treeContainer) {
        treeContainer.innerHTML = `
            <div class="tree-error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button onclick="loadCategories()" class="retry-btn">إعادة المحاولة</button>
            </div>
        `;
    }
}

/**
 * Update categories summary statistics
 */
function updateCategoriesSummary() {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.actif == 1).length;
    const lastUpdated = new Date().toLocaleString('ar-DZ');

    // Update summary elements
    const totalEl = document.getElementById('totalCategories');
    const activeEl = document.getElementById('activeCategories');
    const updatedEl = document.getElementById('lastUpdated');

    if (totalEl) totalEl.textContent = totalCategories;
    if (activeEl) activeEl.textContent = activeCategories;
    if (updatedEl) updatedEl.textContent = lastUpdated;
}

/**
 * Enhanced category search with debouncing
 */
function handleCategorySearch(event) {
    const searchTerm = event.target.value;

    // Clear previous timeout
    if (categorySearchTimeout) {
        clearTimeout(categorySearchTimeout);
    }

    // Set new timeout for debounced search
    categorySearchTimeout = setTimeout(() => {
        filterCategories(searchTerm);
    }, 300);
}

/**
 * Initialize category search functionality
 */
function initializeCategorySearch() {
    const searchInput = document.getElementById('categorySearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleCategorySearch);
    }
}

/**
 * Initialize category reordering (drag and drop)
 */
function initializeCategoryReordering() {
    // This would implement drag and drop functionality
    // For now, we'll just log that it's initialized
    console.log('Category reordering initialized');
}

// Make functions globally available
window.initializeCategoriesManagement = initializeCategoriesManagement;
window.showAddCategoryModal = showAddCategoryModal;
window.showEditCategoryModal = showEditCategoryModal;
window.closeCategoryModal = closeCategoryModal;
window.saveCategory = saveCategory;
window.editCategory = editCategory;
window.deleteCategory = deleteCategory;
window.toggleCategory = toggleCategory;
window.exportCategories = exportCategories;
window.importCategories = importCategories;
window.expandAllCategories = expandAllCategories;
window.collapseAllCategories = collapseAllCategories;

console.log('📋 Categories management script loaded');

// Export functions for global access
window.initializeCategoriesManagement = initializeCategoriesManagement;
window.loadCategories = loadCategories;
window.showAddCategoryModal = showAddCategoryModal;
window.showEditCategoryModal = showEditCategoryModal;
window.closeCategoryModal = closeCategoryModal;
window.saveCategory = saveCategory;
window.exportCategories = exportCategories;
window.importCategories = importCategories;
window.expandAllCategories = expandAllCategories;
window.collapseAllCategories = collapseAllCategories;
window.editCategory = editCategory;
window.deleteCategory = deleteCategory;
