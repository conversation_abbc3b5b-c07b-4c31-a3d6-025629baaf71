<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');

require_once '../config/config.php';
require_once '../config/security.php';
require_once '../config/database.php';
require_once '../config/ai.php';

try {
    Security::init();
    Security::validateRequest();

    // Read and validate JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }

    // Validate providers and API keys
    $providers = ['openai', 'anthropic', 'gemini'];
    $validatedSettings = [];

    foreach ($providers as $provider) {
        if (!isset($input[$provider]) || !isset($input[$provider]['key'])) {
            throw new Exception("Missing settings for $provider");
        }

        $apiKey = trim($input[$provider]['key']);
        $status = Security::checkAIProviderStatus($provider, $apiKey);

        // Get the enabled state from input or default to false
        $isEnabled = isset($input[$provider]['enabled']) ?
            (bool)$input[$provider]['enabled'] :
            false;

        // If the API key is valid, allow it to be enabled
        $validatedSettings[$provider] = [
            'api_key' => $apiKey,
            'model' => $input[$provider]['model'] ?? Config::get(strtoupper($provider) . '_MODEL', 'default'),
            'enabled' => $status['enabled'] && $isEnabled // Only enable if both the key is valid AND user wants it enabled
        ];
    }

    // Save validated settings to database
    $db = Database::getInstance();
    $stmt = $db->prepare("
        INSERT INTO ai_settings (provider, api_key, model, enabled, updated_at)
        VALUES (:provider, :api_key, :model, :enabled, NOW())
        ON DUPLICATE KEY UPDATE
        api_key = VALUES(api_key),
        model = VALUES(model),
        enabled = VALUES(enabled),
        updated_at = NOW()
    ");

    foreach ($validatedSettings as $provider => $settings) {
        $stmt->execute([
            'provider' => $provider,
            'api_key' => $settings['api_key'],
            'model' => $settings['model'],
            'enabled' => $settings['enabled']
        ]);
    }

    // Update environment variables if configured to do so
    if (Config::get('UPDATE_ENV_ON_SAVE', false)) {
        foreach ($validatedSettings as $provider => $settings) {
            $envKey = strtoupper($provider) . '_API_KEY';
            Config::updateEnvVar($envKey, $settings['api_key']);

            $envModel = strtoupper($provider) . '_MODEL';
            Config::updateEnvVar($envModel, $settings['model']);
        }
    }

    $aiConfig = AIManager::getInstance();
    $response = [
        'success' => true,
        'data' => array_map(function ($provider) use ($aiConfig) {
            return $aiConfig->getProviderStatus($provider);
        }, $providers)
    ];

    echo json_encode($response);
} catch (Exception $e) {
    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
