<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');

// Prevent any output before JSON
ob_start();

try {
    // Simple includes without bootstrap to avoid circular dependencies
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../config/database.php';

    // Initialize Config
    Config::init();

    $db = Database::getInstance();
    $pdo = $db->getPDO();

    // Read and validate JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }

    // Process providers and save to database
    $providers = ['openai', 'anthropic', 'gemini'];
    $validatedSettings = [];
    $savedCount = 0;

    foreach ($providers as $provider) {
        if (!isset($input[$provider])) {
            continue; // Skip if provider not in input
        }

        $providerData = $input[$provider];
        $apiKey = trim($providerData['key'] ?? '');
        $enabled = (bool)($providerData['enabled'] ?? false);
        $model = $providerData['model'] ?? getDefaultModel($provider);

        // Save to database (even if API key is empty to preserve enabled state)
        try {
            // Simple encryption for API key storage
            $encryptedKey = !empty($apiKey) ? base64_encode($apiKey) : '';

            $stmt = $pdo->prepare("
                INSERT INTO ai_settings (provider, api_key, enabled, status_message, updated_at)
                VALUES (:provider, :api_key, :enabled, :status_message, NOW())
                ON DUPLICATE KEY UPDATE
                api_key = VALUES(api_key),
                enabled = VALUES(enabled),
                status_message = VALUES(status_message),
                updated_at = NOW()
            ");

            $statusMessage = !empty($apiKey) ? 'Configured' : 'No API key';

            $result = $stmt->execute([
                'provider' => $provider,
                'api_key' => $encryptedKey,
                'enabled' => $enabled ? 1 : 0,
                'status_message' => $statusMessage
            ]);

            if ($result) {
                $validatedSettings[$provider] = [
                    'api_key' => $apiKey,
                    'model' => $model,
                    'enabled' => $enabled,
                    'status' => 'Saved successfully'
                ];
                $savedCount++;
            }
        } catch (Exception $e) {
            $validatedSettings[$provider] = [
                'api_key' => '',
                'model' => $model,
                'enabled' => false,
                'status' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    // Clear any output buffer and return response
    ob_clean();

    echo json_encode([
        'success' => true,
        'data' => $validatedSettings,
        'message' => $savedCount > 0 ? "Successfully saved $savedCount provider(s)" : 'No providers were saved',
        'saved_count' => $savedCount
    ]);
} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getDefaultModel($provider)
{
    $models = [
        'openai' => 'gpt-3.5-turbo',
        'anthropic' => 'claude-3-sonnet-20240229',
        'gemini' => 'gemini-pro'
    ];

    return $models[$provider] ?? 'default';
}
