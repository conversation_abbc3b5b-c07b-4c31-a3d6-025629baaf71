<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');

require_once '../config/bootstrap.php';
require_once '../php/AIManager.php';

try {
    $aiManager = AIManager::getInstance();

    // Read and validate JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }

    // Validate providers and API keys
    $providers = ['openai', 'anthropic', 'gemini'];
    $validatedSettings = [];
    $savedCount = 0;

    foreach ($providers as $provider) {
        if (!isset($input[$provider])) {
            continue; // Skip if provider not in input
        }

        $providerData = $input[$provider];
        $apiKey = trim($providerData['key'] ?? '');
        $enabled = (bool)($providerData['enabled'] ?? false);
        $model = $providerData['model'] ?? $aiManager->getDefaultModel($provider);

        // Only process if API key is provided
        if (!empty($apiKey)) {
            try {
                // Update provider configuration using AIManager
                $result = $aiManager->updateProviderConfig($provider, $apiKey, $enabled);

                if ($result) {
                    $validatedSettings[$provider] = [
                        'api_key' => $apiKey,
                        'model' => $model,
                        'enabled' => $enabled,
                        'status' => 'Saved successfully'
                    ];
                    $savedCount++;
                }
            } catch (Exception $e) {
                $validatedSettings[$provider] = [
                    'api_key' => '',
                    'model' => $model,
                    'enabled' => false,
                    'status' => 'Error: ' . $e->getMessage()
                ];
            }
        }
    }

    // Return response
    echo json_encode([
        'success' => true,
        'data' => $validatedSettings,
        'message' => $savedCount > 0 ? "Successfully saved $savedCount provider(s)" : 'No providers were saved',
        'saved_count' => $savedCount
    ]);
} catch (Exception $e) {
    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
