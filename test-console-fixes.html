<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Console Fixes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Console Error Fixes Test</h1>
        <p>This page tests the fixes for console errors in the admin panel.</p>
        
        <div id="results"></div>
        
        <h3>Test Actions:</h3>
        <button onclick="testDashboardStats()">Test Dashboard Stats API</button>
        <button onclick="testAISettings()">Test AI Settings API</button>
        <button onclick="testSelectionAPI()">Test Selection API</button>
        <button onclick="testContextMenu()">Test Context Menu</button>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        async function testDashboardStats() {
            addResult('Testing Dashboard Stats API...', 'info');
            try {
                const response = await fetch('../php/api/dashboard-stats.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult('✅ Dashboard Stats API working correctly', 'success');
                    addResult(`📊 Found ${data.data.products.total} products, ${data.data.orders.total} orders`, 'info');
                } else {
                    addResult(`❌ Dashboard Stats API error: ${data.message}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Dashboard Stats API failed: ${error.message}`, 'error');
            }
        }
        
        async function testAISettings() {
            addResult('Testing AI Settings API...', 'info');
            try {
                const response = await fetch('../api/get-ai-settings.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult('✅ AI Settings API working correctly', 'success');
                    const providers = Object.keys(data.data);
                    addResult(`🤖 Found AI providers: ${providers.join(', ')}`, 'info');
                } else {
                    addResult(`❌ AI Settings API error: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ AI Settings API failed: ${error.message}`, 'error');
            }
        }
        
        function testSelectionAPI() {
            addResult('Testing Selection API...', 'info');
            try {
                const selection = window.getSelection();
                if (selection) {
                    addResult(`✅ Selection API working: rangeCount = ${selection.rangeCount}`, 'success');
                    
                    // Test problematic operations
                    try {
                        selection.removeAllRanges();
                        addResult('✅ removeAllRanges() working', 'success');
                    } catch (error) {
                        addResult(`❌ removeAllRanges() failed: ${error.message}`, 'error');
                    }
                    
                    try {
                        const range = document.createRange();
                        selection.addRange(range);
                        addResult('✅ addRange() working', 'success');
                    } catch (error) {
                        addResult(`❌ addRange() failed: ${error.message}`, 'error');
                    }
                } else {
                    addResult('❌ Selection API returned null', 'error');
                }
            } catch (error) {
                addResult(`❌ Selection API failed: ${error.message}`, 'error');
            }
        }
        
        function testContextMenu() {
            addResult('Testing Context Menu...', 'info');
            try {
                // Simulate a context menu event
                const event = new MouseEvent('contextmenu', {
                    bubbles: true,
                    cancelable: true,
                    clientX: 100,
                    clientY: 100
                });
                
                document.body.dispatchEvent(event);
                addResult('✅ Context menu event dispatched successfully', 'success');
            } catch (error) {
                addResult(`❌ Context menu test failed: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            addResult('🚀 Running all tests...', 'info');
            results.innerHTML = '';
            
            await testDashboardStats();
            await testAISettings();
            testSelectionAPI();
            testContextMenu();
            
            addResult('✅ All tests completed', 'success');
        }
        
        // Test on page load
        window.addEventListener('DOMContentLoaded', function() {
            addResult('🔧 Console Error Fixes Test Page Loaded', 'success');
            addResult('Click the buttons above to test individual fixes', 'info');
        });
        
        // Global error handler to catch any remaining errors
        window.addEventListener('error', function(event) {
            addResult(`🚨 Global error caught: ${event.error.message}`, 'error');
        });
        
        // Test selection change events
        document.addEventListener('selectionchange', function() {
            console.log('🛡️ Selection change event handled safely');
        });
    </script>
</body>
</html>
