// Gestion du panier
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Mise à jour du compteur du panier
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
}

// Obtenir les détails spécifiques au type de produit
function getProductTypeDetails(product) {
    let details = '';

    switch (product.type) {
        case 'book':
            if (product.auteur) {
                details += `<p class="author">${product.auteur}</p>`;
            }
            break;
        case 'bag':
            if (product.materiel) {
                details += `<p>المواد: ${product.materiel}</p>`;
            }
            if (product.capacite) {
                details += `<p>السعة: ${product.capacite}</p>`;
            }
            break;
        case 'laptop':
            if (product.processeur) {
                details += `<p>المعالج: ${product.processeur}</p>`;
            }
            if (product.ram) {
                details += `<p>الذاكرة: ${product.ram}</p>`;
            }
            if (product.stockage) {
                details += `<p>التخزين: ${product.stockage}</p>`;
            }
            break;
    }

    return details;
}

// Charger les produits
async function loadProducts() {
    try {
        console.log('Loading products...');
        const data = await safeApiCall('php/api/products.php');

        if (!data) {
            showNotification('لا توجد منتجات متاحة حالياً', 'warning');
            return;
        }

        console.log('Parsed data:', data);

        // Handle new standardized API response format
        if (data.success && data.data && Array.isArray(data.data)) {
            const booksGrid = document.querySelector('.books-grid');
            booksGrid.innerHTML = '';

            data.data.forEach(product => {
                if (product.actif) {
                    const productCard = document.createElement('div');
                    productCard.className = 'book-card';
                    productCard.setAttribute('data-type', product.type);

                    const typeDetails = getProductTypeDetails(product);

                    // Use optimized images with lazy loading
                    const imageUrl = product.image_urls ? product.image_urls.medium : (product.image_url || 'images/book1.svg');
                    const thumbnailUrl = product.image_urls ? product.image_urls.thumbnail : imageUrl;

                    productCard.innerHTML = `
                        <img data-src="${imageUrl}"
                             src="${thumbnailUrl}"
                             alt="${product.titre}"
                             class="product-image lazy-load"
                             loading="lazy">
                        <div class="book-info">
                            <h3>${product.titre}</h3>
                            ${typeDetails}
                            <p class="price">${product.prix} دج</p>
                            <div class="product-details">
                                ${product.description || ''}
                            </div>
                            <div class="buttons">
                                <button class="add-to-cart">إضافة إلى السلة</button>
                                <button class="buy-now">شراء الآن</button>
                            </div>
                        </div>
                    `;

                    // Ajouter les gestionnaires d'événements
                    const addToCartBtn = productCard.querySelector('.add-to-cart');
                    const buyNowBtn = productCard.querySelector('.buy-now');

                    addToCartBtn.addEventListener('click', () => {
                        addToCart(product.id, product.titre, product.prix, product.image_url);
                    });

                    buyNowBtn.addEventListener('click', () => {
                        window.location.href = 'checkout.html';
                    });

                    booksGrid.appendChild(productCard);
                }
            });

            console.log(`Loaded ${data.data.length} products successfully`);

            // Initialize lazy loading for new images
            if (typeof LazyLoadManager !== 'undefined') {
                const booksGrid = document.querySelector('.books-grid');
                LazyLoadManager.observeNewImages(booksGrid);
            }

            if (data.data.length === 0) {
                const booksGrid = document.querySelector('.books-grid');
                booksGrid.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">لا توجد منتجات متاحة حالياً</p>';
            }
        } else {
            console.error('Invalid API response format:', data);
            console.error('Expected: {success: true, data: [...]}');
            showNotification('خطأ في تحميل المنتجات - تنسيق غير صحيح', 'error');

            // Show fallback message
            const booksGrid = document.querySelector('.books-grid');
            if (booksGrid) {
                booksGrid.innerHTML = '<p style="text-align: center; color: #e74c3c; padding: 2rem;">خطأ في تنسيق البيانات. يرجى المحاولة مرة أخرى.</p>';
            }
        }
    } catch (error) {
        console.error('Erreur lors du chargement des produits:', error);
        showNotification('حدث خطأ أثناء تحميل المنتجات', 'error');

        // Show fallback message
        const booksGrid = document.querySelector('.books-grid');
        if (booksGrid) {
            booksGrid.innerHTML = '<p style="text-align: center; color: #e74c3c; padding: 2rem;">حدث خطأ أثناء تحميل المنتجات. يرجى المحاولة مرة أخرى.</p>';
        }
    }
}

// Ajouter au panier
function addToCart(productId, title, price, image) {
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            title: title,
            price: price,
            image: image,
            quantity: 1
        });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();

    // Animation de confirmation
    showNotification('تمت إضافة المنتج إلى السلة');
}

// Afficher une notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;

    document.body.appendChild(notification);

    // Ajouter le style CSS pour la notification
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #2ecc71;
        color: white;
        padding: 15px 25px;
        border-radius: 5px;
        animation: slideIn 0.5s ease-out;
        z-index: 1000;
        font-family: 'Noto Sans Arabic', sans-serif;
    `;

    // Ajouter l'animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    // Supprimer la notification après 3 secondes
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

// Initialiser les événements
document.addEventListener('DOMContentLoaded', () => {
    updateCartCount();
    loadProducts();
});
