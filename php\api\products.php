<?php
// Define security check constant BEFORE including security.php - only if not already defined
if (!defined('SECURITY_CHECK')) {
    define('SECURITY_CHECK', true);
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiUtils.php';
require_once __DIR__ . '/../ImageOptimizer.php';
require_once __DIR__ . '/../CacheManager.php';
require_once __DIR__ . '/../security.php';
require_once __DIR__ . '/../SecurityHeaders.php';

// Set security headers for XSS protection
SecurityHeaders::setSecurityHeaders();

// Handle CORS
ApiUtils::handleCors();

// Initialize cache
$cache = new CacheManager('../cache/', 1800); // 30 minutes cache

function handleGet()
{
    global $conn, $cache;

    try {
        // Check if requesting a specific product
        $id = $_GET['id'] ?? null;

        if ($id) {
            // Try to get from cache first
            $cacheKey = "product_$id";
            $cachedProduct = $cache->get($cacheKey);

            if ($cachedProduct !== null) {
                CacheManager::setCacheHeaders(1800); // 30 minutes
                ApiUtils::sendSuccess($cachedProduct, 'Product loaded from cache');
                return;
            }
            // Get specific product
            $stmt = $conn->prepare(
                "SELECT p.*,
                        CASE
                            WHEN lp.id IS NOT NULL THEN true
                            ELSE false
                        END as has_landing_page,
                        lp.lien_url as landing_url
                 FROM produits p
                 LEFT JOIN (
                     SELECT produit_id, id, lien_url
                     FROM landing_pages
                     GROUP BY produit_id
                 ) lp ON p.id = lp.produit_id
                 WHERE p.id = ?"
            );
            $stmt->execute([$id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$product) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Product not found']);
                return;
            }

            // Cache the result
            $cache->set($cacheKey, $product, 1800); // 30 minutes

            echo json_encode(['success' => true, 'data' => $product]);
            return;
        }

        // Get all products - check cache first
        $cacheKey = 'products_all';
        $cachedProducts = $cache->get($cacheKey);

        if ($cachedProducts !== null) {
            CacheManager::setCacheHeaders(1800); // 30 minutes
            ApiUtils::sendSuccess($cachedProducts, 'Products loaded from cache');
            return;
        }

        // Cache miss - fetch from database
        $stmt = $conn->prepare(
            "SELECT p.*,
                    CASE
                        WHEN lp.id IS NOT NULL THEN true
                        ELSE false
                    END as has_landing_page,
                    lp.lien_url as landing_url
             FROM produits p
             LEFT JOIN (
                 SELECT produit_id, id, lien_url
                 FROM landing_pages
                 GROUP BY produit_id
             ) lp ON p.id = lp.produit_id"
        );
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($products)) {
            echo json_encode(['success' => true, 'products' => []]);
            return;
        }

        // Add full URLs and optimized images
        $imageOptimizer = new ImageOptimizer('uploads/products/');
        foreach ($products as &$product) {
            $product['url'] = '/Mossaab-LandingPage/product.php?id=' . $product['id'];
            if ($product['has_landing_page']) {
                $product['landing_url'] = '/Mossaab-LandingPage/landing-page.php?id=' . $product['id'];
            }

            // Add optimized image URLs
            if ($product['image_url']) {
                $filename = basename($product['image_url']);
                $product['image_urls'] = [
                    'thumbnail' => $imageOptimizer->getOptimizedImageUrl($filename, 'thumbnail'),
                    'medium' => $imageOptimizer->getOptimizedImageUrl($filename, 'medium'),
                    'large' => $imageOptimizer->getOptimizedImageUrl($filename, 'large'),
                    'original' => $imageOptimizer->getOptimizedImageUrl($filename, 'original')
                ];
            }
        }

        // Cache the results
        $cache->set($cacheKey, $products, 1800); // Cache for 30 minutes

        CacheManager::setCacheHeaders(1800); // 30 minutes
        ApiUtils::sendSuccess($products, 'Products loaded successfully');
    } catch (PDOException $e) {
        ApiUtils::sendError('Database error: ' . $e->getMessage(), 500, 'DB_ERROR');
    }
}

function handleToggleActive()
{
    global $conn;

    try {
        // Enhanced error logging
        error_log("Toggle active called with POST data: " . print_r($_POST, true));

        $id = $_POST['productId'] ?? null;
        $active = isset($_POST['active']) ? (bool)$_POST['active'] : null;

        if (!$id || $active === null) {
            error_log("Toggle active validation failed - ID: $id, Active: " . var_export($active, true));
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Product ID and active status are required']);
            return;
        }

        // Convert string values to proper boolean
        if (is_string($_POST['active'])) {
            $active = $_POST['active'] === '1' || $_POST['active'] === 'true';
        }

        error_log("Updating product $id to active status: " . ($active ? 'true' : 'false'));

        $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
        $result = $stmt->execute([$active ? 1 : 0, $id]);

        if ($result && $stmt->rowCount() > 0) {
            error_log("Product $id status updated successfully");
            echo json_encode(['success' => true, 'message' => 'Product status updated successfully']);
        } else {
            error_log("Product $id not found or no changes made");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Product not found or no changes made']);
        }
    } catch (PDOException $e) {
        error_log("Database error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    } catch (Exception $e) {
        error_log("General error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
    }
}

function handlePost()
{
    error_log("========= DEBUG PRODUCT SUBMISSION ==========");
    error_log("Raw POST data: " . file_get_contents('php://input'));
    error_log("Content-Type header: " . $_SERVER['CONTENT_TYPE']);
    error_log("Description content: " . (isset($_POST['productDescription']) ? $_POST['productDescription'] : 'not set'));
    error_log("Description length: " . (isset($_POST['productDescription']) ? strlen($_POST['productDescription']) : 0));
    error_log("========= END DEBUG PRODUCT SUBMISSION ==========");

    error_log("Received POST request with data: " . print_r($_POST, true));
    error_log("Received FILES: " . print_r($_FILES, true));
    global $conn, $cache;

    try {
        // Rate limiting check
        if (!checkRateLimit('product_submit', 10, 3600)) {
            return; // Error response sent by checkRateLimit
        }

        // Get and sanitize input data
        $id = $_POST['productId'] ?? null;
        $title = sanitizeInput($_POST['productTitle'] ?? '');
        $description = sanitizeInput($_POST['productDescription'] ?? '', true); // Allow HTML for rich text
        $price = sanitizeInput($_POST['productPrice'] ?? '');
        $stock = sanitizeInput($_POST['productStock'] ?? '');
        $type = sanitizeInput($_POST['productType'] ?? '');

        // Comprehensive input validation
        $validationErrors = [];

        // Validate title (Arabic text)
        $titleValidation = validateArabicText($title, 2, 200);
        if (!$titleValidation['valid']) {
            $validationErrors[] = 'عنوان المنتج: ' . $titleValidation['error'];
        }

        // Validate description (Arabic text with HTML)
        if (!empty($description)) {
            $descValidation = validateArabicText(strip_tags($description), 10, 2000);
            if (!$descValidation['valid']) {
                $validationErrors[] = 'وصف المنتج: ' . $descValidation['error'];
            }
        }

        // Validate price
        $priceValidation = validatePrice($price);
        if (!$priceValidation['valid']) {
            $validationErrors[] = 'سعر المنتج: ' . $priceValidation['error'];
        } else {
            $price = $priceValidation['value'];
        }

        // Validate stock
        if (!is_numeric($stock) || intval($stock) < 0) {
            $validationErrors[] = 'كمية المخزون يجب أن تكون رقماً موجباً';
        } else {
            $stock = intval($stock);
        }

        // Validate product type
        $allowedTypes = ['book', 'bag', 'laptop'];
        if (!in_array($type, $allowedTypes)) {
            $validationErrors[] = 'نوع المنتج غير صحيح';
        }

        // If there are validation errors, return them
        if (!empty($validationErrors)) {
            ApiUtils::sendError('أخطاء في البيانات المدخلة: ' . implode(', ', $validationErrors), 400, 'VALIDATION_FAILED');
            return;
        }

        // Handle and validate specific fields based on product type
        $specificFields = [];
        switch ($type) {
            case 'book':
                $author = sanitizeInput($_POST['productAuthor'] ?? '');
                if (!empty($author)) {
                    $authorValidation = validateArabicText($author, 2, 100);
                    if (!$authorValidation['valid']) {
                        $validationErrors[] = 'اسم المؤلف: ' . $authorValidation['error'];
                    } else {
                        $specificFields['auteur'] = $author;
                    }
                }
                break;

            case 'bag':
                $material = sanitizeInput($_POST['bagMaterial'] ?? '');
                $capacity = sanitizeInput($_POST['bagCapacity'] ?? '');

                if (!empty($material)) {
                    $materialValidation = validateArabicText($material, 2, 50);
                    if (!$materialValidation['valid']) {
                        $validationErrors[] = 'مادة الحقيبة: ' . $materialValidation['error'];
                    } else {
                        $specificFields['materiel'] = $material;
                    }
                }

                if (!empty($capacity)) {
                    if (!preg_match('/^\d+(\.\d+)?\s*(لتر|L|ليتر)$/u', $capacity)) {
                        $validationErrors[] = 'سعة الحقيبة يجب أن تكون بالصيغة الصحيحة (مثال: 20 لتر)';
                    } else {
                        $specificFields['capacite'] = $capacity;
                    }
                }
                break;

            case 'laptop':
                $processor = sanitizeInput($_POST['laptopProcessor'] ?? '');
                $ram = sanitizeInput($_POST['laptopRam'] ?? '');
                $storage = sanitizeInput($_POST['laptopStorage'] ?? '');

                if (!empty($processor)) {
                    if (strlen($processor) < 3 || strlen($processor) > 100) {
                        $validationErrors[] = 'معالج اللابتوب يجب أن يكون بين 3 و 100 حرف';
                    } else {
                        $specificFields['processeur'] = $processor;
                    }
                }

                if (!empty($ram)) {
                    if (!preg_match('/^\d+\s*(GB|جيجا|جيجابايت)$/u', $ram)) {
                        $validationErrors[] = 'ذاكرة الوصول العشوائي يجب أن تكون بالصيغة الصحيحة (مثال: 8 GB)';
                    } else {
                        $specificFields['ram'] = $ram;
                    }
                }

                if (!empty($storage)) {
                    if (!preg_match('/^\d+\s*(GB|TB|جيجا|تيرا|جيجابايت|تيرابايت)$/u', $storage)) {
                        $validationErrors[] = 'مساحة التخزين يجب أن تكون بالصيغة الصحيحة (مثال: 256 GB)';
                    } else {
                        $specificFields['stockage'] = $storage;
                    }
                }
                break;
        }

        // Check for validation errors again after specific field validation
        if (!empty($validationErrors)) {
            ApiUtils::sendError('أخطاء في البيانات المدخلة: ' . implode(', ', $validationErrors), 400, 'VALIDATION_FAILED');
            return;
        }

        $conn->beginTransaction();

        // Handle image upload with enhanced security validation
        $imageUrl = null;
        if (!empty($_FILES['productImage']['tmp_name'])) {
            // Validate uploaded file first
            $fileValidation = validateFileUpload(
                $_FILES['productImage'],
                ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                5242880 // 5MB max
            );

            if (!$fileValidation['valid']) {
                ApiUtils::sendError('خطأ في رفع الصورة: ' . $fileValidation['error'], 400, 'FILE_UPLOAD_ERROR');
                return;
            }

            // Log successful file validation
            logSecurityEvent('FILE_UPLOAD_VALIDATED', 'Product image upload validated: ' . $_FILES['productImage']['name']);

            $imageOptimizer = new ImageOptimizer('../../uploads/products/');
            $result = $imageOptimizer->processUploadedImage($_FILES['productImage']);

            if ($result['success']) {
                $imageUrl = '/uploads/products/' . $result['filename'];
                error_log("Image optimized successfully: " . print_r($result['variants'], true));
                logSecurityEvent('IMAGE_OPTIMIZED', 'Product image optimized: ' . $result['filename']);
            } else {
                throw new Exception('فشل في تحسين الصورة: ' . $result['error']);
            }
        }

        if ($id) {
            // Update existing product
            $sql = "UPDATE produits SET
                    titre = ?,
                    description = ?,
                    prix = ?,
                    stock = ?,
                    type = ?";
            $params = [$title, $description, $price, $stock, $type];

            if ($imageUrl) {
                $sql .= ", image_url = ?";
                $params[] = $imageUrl;
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $sql .= ", $field = ?";
                    $params[] = $value;
                }
            }

            $sql .= " WHERE id = ?";
            $params[] = $id;

            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
        } else {
            // Insert new product
            $fields = ['titre', 'description', 'prix', 'stock', 'type'];
            $values = [$title, $description, $price, $stock, $type];
            $placeholders = ['?', '?', '?', '?', '?'];

            if ($imageUrl) {
                $fields[] = 'image_url';
                $values[] = $imageUrl;
                $placeholders[] = '?';
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }

            $sql = "INSERT INTO produits (" . implode(', ', $fields) . ")
                   VALUES (" . implode(', ', $placeholders) . ")";

            error_log("SQL Query: " . $sql);
            error_log("Values: " . print_r($values, true));

            $stmt = $conn->prepare($sql);
            $stmt->execute($values);
            $id = $conn->lastInsertId();

            error_log("New product ID: " . $id);

            // Verify the insertion
            $verifyStmt = $conn->prepare("SELECT * FROM produits WHERE id = ?");
            $verifyStmt->execute([$id]);
            $newProduct = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            error_log("Newly inserted product: " . print_r($newProduct, true));
        }

        $conn->commit();

        // Invalidate cache after successful update
        $cache->delete('products_all');
        if ($id) {
            $cache->delete("product_$id");
        }

        ApiUtils::sendSuccess(['id' => $id], 'Product saved successfully');
    } catch (PDOException $e) {
        $conn->rollBack();
        ApiUtils::sendError('Database error: ' . $e->getMessage(), 500, 'DB_ERROR');
    } catch (Exception $e) {
        $conn->rollBack();
        ApiUtils::sendError('Server error: ' . $e->getMessage(), 500, 'SERVER_ERROR');
    }
}

function handleDelete()
{
    global $conn;

    try {
        // Check if this is a bulk delete request
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if ($data && isset($data['action']) && $data['action'] === 'bulk_delete' && isset($data['ids'])) {
            // Handle bulk deletion
            $ids = $data['ids'];

            if (empty($ids) || !is_array($ids)) {
                throw new Exception('No valid product IDs provided for bulk deletion');
            }

            // Validate all IDs are numeric
            foreach ($ids as $id) {
                if (!is_numeric($id)) {
                    throw new Exception('Invalid product ID provided');
                }
            }

            $conn->beginTransaction();

            $deletedCount = 0;
            $imagesToDelete = [];

            // Get image paths before deleting
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $stmt = $conn->prepare("SELECT id, image_url FROM produits WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($products as $product) {
                if ($product['image_url']) {
                    $imagesToDelete[] = $product['image_url'];
                }
            }

            // Delete products (cascade will handle related records)
            $stmt = $conn->prepare("DELETE FROM produits WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $deletedCount = $stmt->rowCount();

            $conn->commit();

            // Delete physical image files
            foreach ($imagesToDelete as $imageUrl) {
                $filePath = '../../' . ltrim($imageUrl, '/');
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "Successfully deleted $deletedCount products",
                'deleted_count' => $deletedCount
            ]);
        } else {
            // Handle single product deletion (existing functionality)
            $id = $_GET['id'] ?? null;

            if (!$id) {
                throw new Exception('Product ID is required');
            }

            // Get image path before deleting
            $stmt = $conn->prepare("SELECT image_url FROM produits WHERE id = ?");
            $stmt->execute([$id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            // Delete product (cascade will handle landing pages)
            $stmt = $conn->prepare("DELETE FROM produits WHERE id = ?");
            $stmt->execute([$id]);

            // Delete physical image file if exists
            if ($product && $product['image_url']) {
                $filePath = '../../' . ltrim($product['image_url'], '/');
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
        }
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in handleDelete: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in handleDelete: ' . $e->getMessage());

        // Return 400 for validation errors, 500 for server errors
        $statusCode = (strpos($e->getMessage(), 'No valid') !== false ||
            strpos($e->getMessage(), 'Invalid') !== false) ? 400 : 500;

        http_response_code($statusCode);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// Route requests
// Get request type
$action = $_GET['action'] ?? '';

// Log request details
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Request Action: " . $action);
// Only log headers if function exists (not in CLI mode)
if (function_exists('getallheaders')) {
    error_log("Request Headers: " . print_r(getallheaders(), true));
}

// Route requests
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
    case 'PUT':
        if ($action === 'toggle-active') {
            handleToggleActive();
        } else {
            handlePost();
        }
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
