<?php
require_once __DIR__ . '/../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $pdo = getPDOConnection();

    // Statistiques des produits
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_products FROM produits");
    $stmt->execute();
    $totalProducts = $stmt->fetch()['total_products'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as active_products FROM produits WHERE actif = 1");
    $stmt->execute();
    $activeProducts = $stmt->fetch()['active_products'];

    // Statistiques des commandes
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_orders FROM commandes");
    $stmt->execute();
    $totalOrders = $stmt->fetch()['total_orders'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as pending_orders FROM commandes WHERE statut = 'en_attente'");
    $stmt->execute();
    $pendingOrders = $stmt->fetch()['pending_orders'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as paid_orders FROM commandes WHERE statut = 'payé'");
    $stmt->execute();
    $paidOrders = $stmt->fetch()['paid_orders'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as shipped_orders FROM commandes WHERE statut = 'expédié'");
    $stmt->execute();
    $shippedOrders = $stmt->fetch()['shipped_orders'];

    // Statistiques des ventes
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(montant_total), 0) as total_sales FROM commandes WHERE statut IN ('payé', 'expédié')");
    $stmt->execute();
    $totalSales = $stmt->fetch()['total_sales'];

    $stmt = $pdo->prepare("SELECT COALESCE(SUM(montant_total), 0) as pending_sales FROM commandes WHERE statut = 'en_attente'");
    $stmt->execute();
    $pendingSales = $stmt->fetch()['pending_sales'];

    // Statistiques des landing pages
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_landing_pages FROM landing_pages");
    $stmt->execute();
    $totalLandingPages = $stmt->fetch()['total_landing_pages'];

    // Statistiques des livres (si la table existe)
    $totalBooks = 0;
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_books FROM livres");
        $stmt->execute();
        $totalBooks = $stmt->fetch()['total_books'];
    } catch (PDOException $e) {
        // Table livres n'existe pas, on ignore
        $totalBooks = 0;
    }

    // Statistiques des utilisateurs
    $totalUsers = 0;
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_users FROM users");
        $stmt->execute();
        $totalUsers = $stmt->fetch()['total_users'];
    } catch (PDOException $e) {
        // Table users might not exist
        $totalUsers = 0;
    }

    // Ventes par mois (derniers 6 mois)
    $stmt = $pdo->prepare("
        SELECT
            DATE_FORMAT(date_commande, '%Y-%m') as month,
            COALESCE(SUM(montant_total), 0) as sales
        FROM commandes
        WHERE statut IN ('payé', 'expédié')
        AND date_commande >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(date_commande, '%Y-%m')
        ORDER BY month DESC
        LIMIT 6
    ");
    $stmt->execute();
    $monthlySales = $stmt->fetchAll();

    // Produits les plus vendus
    $stmt = $pdo->prepare("
        SELECT
            p.titre,
            p.prix,
            COALESCE(SUM(dc.quantite), 0) as total_sold
        FROM produits p
        LEFT JOIN details_commande dc ON p.id = dc.livre_id
        LEFT JOIN commandes c ON dc.commande_id = c.id
        WHERE c.statut IN ('payé', 'expédié') OR c.statut IS NULL
        GROUP BY p.id, p.titre, p.prix
        ORDER BY total_sold DESC
        LIMIT 5
    ");
    $stmt->execute();
    $topProducts = $stmt->fetchAll();

    // Commandes récentes
    $stmt = $pdo->prepare("
        SELECT
            c.*,
            COUNT(dc.id) as items_count
        FROM commandes c
        LEFT JOIN details_commande dc ON c.id = dc.commande_id
        GROUP BY c.id
        ORDER BY c.date_commande DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentOrders = $stmt->fetchAll();

    // Réponse JSON
    echo json_encode([
        'success' => true,
        'data' => [
            'products' => [
                'total' => (int)$totalProducts,
                'active' => (int)$activeProducts,
                'inactive' => (int)($totalProducts - $activeProducts)
            ],
            'orders' => [
                'total' => (int)$totalOrders,
                'pending' => (int)$pendingOrders,
                'paid' => (int)$paidOrders,
                'shipped' => (int)$shippedOrders
            ],
            'sales' => [
                'total' => (float)$totalSales,
                'pending' => (float)$pendingSales,
                'formatted_total' => number_format($totalSales, 2) . ' دج',
                'formatted_pending' => number_format($pendingSales, 2) . ' دج'
            ],
            'landing_pages' => [
                'total' => (int)$totalLandingPages
            ],
            'books' => [
                'total' => (int)$totalBooks
            ],
            'users' => [
                'total' => (int)$totalUsers
            ],
            'charts' => [
                'monthly_sales' => $monthlySales,
                'top_products' => $topProducts
            ],
            'recent_orders' => $recentOrders
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} catch (PDOException $e) {
    error_log("Database error in dashboard-stats.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur de base de données',
        'error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    error_log("General error in dashboard-stats.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur serveur',
        'error' => $e->getMessage()
    ]);
}
